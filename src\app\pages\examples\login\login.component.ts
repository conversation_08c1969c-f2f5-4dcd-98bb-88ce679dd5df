import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { AuthService } from "src/app/core/services/auth-service.service";
import { TokenService } from "src/app/core/services/token-service.service";


@Component({
  selector: "app-login",
  templateUrl: "login.component.html"
})
export class LoginComponent implements OnInit {
  focus: boolean = false;
  focus1: boolean = false;
  authForm: FormGroup;
  constructor(
    private authService: AuthService,
    private fb: FormBuilder,
    private router: Router,

    private tokenService: TokenService
  ) {}

  ngOnInit(): void {
    this.authForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      mdp: ['', [Validators.required, Validators.minLength(6)]],
      recaptcha: ['', Validators.required],
    });
  }
  errorMessage: string | null = null; // Add this property in your component
  submit() {
    console.log("submit");
    console.log(this.authForm.value);

    // Clear previous error messages
    this.errorMessage = null;

    // Mark all fields as touched to show validation errors
    Object.keys(this.authForm.controls).forEach(key => {
      this.authForm.get(key)?.markAsTouched();
    });

    if (this.authForm.valid) {
      console.log("form valid");
      const { email, mdp, recaptcha } = this.authForm.value;

      // Validate reCAPTCHA token
      if (!recaptcha || recaptcha.trim() === '') {
        this.errorMessage = 'Veuillez valider le CAPTCHA';
        return;
      }

      const authValues = { username: email, password: mdp };
      console.log('Attempting authentication with:', { username: email, password: '***' });
      
      this.authService.authenticate(authValues).subscribe({
        next: (data: any) => {
          console.log('Authentication successful:', data);
          // Store tokens using the token service
          this.tokenService.saveTokens(data);
          
          // Decode the token to get roles
          const tokenPayload = this.decodeToken(data.access_token);
          console.log('Token payload:', tokenPayload);
          
          const roles = tokenPayload?.resource_access?.['BNG-Elearning-rest-api']?.roles || [];
          console.log('User roles:', roles);

          // Fetch user data based on the email
          this.authService.getUserByEmail(email).subscribe({
            next: (userData: any) => {
              console.log('User data fetched:', userData);

              // You can now check the user's role and navigate accordingly
              if (userData.role == 'ADMINISTRATEUR') {
                this.router.navigate(['/admin/dashboard']);
              } else  {
                window.location.href = 'http://localhost:4200/';
              }
            },
            error: (error) => {
              console.error('Failed to fetch user by email:', error);
              this.errorMessage =
                error.error?.message || 'Failed to retrieve user data.';
            },
          });
        },
        error: (error) => {
          console.error('Authentication failed:', error);
          if (error.status === 401) {
            this.errorMessage = 'Email ou mot de passe incorrect';
          } else if (error.status === 0) {
            this.errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion.';
          } else {
            this.errorMessage = error.error?.error_description || error.error?.error || 'Échec de l\'authentification. Veuillez réessayer.';
          }
        },
      });
    } else {
      console.log("form invalid");
      this.errorMessage = 'Veuillez corriger les erreurs dans le formulaire';
    }
  }

  decodeToken(token: string): any {
    try {
      const payload = token.split('.')[1]; // Get the payload part
      const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(window.atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.authForm.get(fieldName);
    return field ? field.invalid && field.touched : false;
  }
  getErrorMessage(controlName: string): string {
    const control = this.authForm.get(controlName);
    if (control?.hasError('required')) {
      if (controlName === 'recaptcha') {
        return 'Veuillez valider le CAPTCHA';
      }
      if (controlName === 'email') {
        return 'L\'email est obligatoire';
      }
      if (controlName === 'mdp') {
        return 'Le mot de passe est obligatoire';
      }
      return 'Ce champ est obligatoire';
    }
    if (control?.hasError('email')) {
      return 'Veuillez entrer un email valide';
    }
    if (control?.hasError('minlength')) {
      return 'Le mot de passe doit contenir au moins 6 caractères';
    }
    return '';
  }

  onCaptchaResolved(token: string) {
    console.log('Captcha resolved with token:', token);
    this.authForm.get('recaptcha')?.setValue(token);
  }

  onCaptchaExpired() {
    console.log('Captcha expired');
    this.authForm.get('recaptcha')?.setValue('');
    this.errorMessage = 'reCAPTCHA expired. Please verify again.';
  }

  onCaptchaError(error: any) {
    console.error('Captcha error:', error);
    this.authForm.get('recaptcha')?.setValue('');
    this.errorMessage = 'reCAPTCHA error. Please try again.';
  }
}


