{"name": "argon-dashboard-pro-angular", "version": "1.5.1", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start"}, "private": true, "dependencies": {"@angular/animations": "^15.2.0", "@angular/common": "^15.2.0", "@angular/compiler": "^15.2.0", "@angular/core": "^15.2.0", "@angular/forms": "^15.2.0", "@angular/platform-browser": "^15.2.0", "@angular/platform-browser-dynamic": "^15.2.0", "@angular/router": "^15.2.0", "@fullcalendar/core": "6.1.8", "@fullcalendar/daygrid": "6.1.8", "@fullcalendar/interaction": "6.1.8", "@swimlane/ngx-datatable": "20.1.0", "ajv": "8.12.0", "bootstrap": "4.5.2", "chart.js": "2.9.3", "clipboard": "2.0.11", "d3-scale": "4.0.2", "date-fns": "2.30.0", "devextreme": "22.2.6", "devextreme-angular": "22.2.6", "dropzone": "5.9.3", "fullcalendar": "5.10.2", "jquery": "^3.6.0", "jwt-decode": "^4.0.0", "list.js": "2.3.1", "mobius1-selectr": "2.4.13", "ngx-bootstrap": "10.2.0", "ngx-chips": "3.0.0", "ngx-clipboard": "15.1.0", "ngx-perfect-scrollbar": "10.1.1", "ngx-print": "1.3.1", "ngx-toastr": "15.2.2", "nouislider": "15.7.1", "npm-install-peers": "^1.2.2", "popper.js": "1.16.1", "quill": "1.3.7", "rxjs": "~7.8.0", "stream": "0.0.2", "sweetalert2": "11.4.6", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.8", "@angular/cli": "~15.2.8", "@angular/compiler-cli": "^15.2.0", "@angular/language-service": "15.2.0", "@types/bootstrap": "4.5.0", "@types/chartist": "0.11.1", "@types/jasmine": "~4.3.0", "@types/jasminewd2": "~2.0.10", "@types/jquery": "3.5.6", "@types/node": "^17.0.21", "codelyzer": "6.0.2", "jasmine-core": "~4.5.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "protractor": "7.0.0", "ts-node": "~10.9.1", "typescript": "~4.9.4"}}