<div class=" header bg-danger pb-6">
  <div class=" container-fluid">
    <div class=" header-body">
      <div class=" row align-items-center py-4">
        <div class=" col-lg-6 col-7">
          <h6 class=" h2 text-white d-inline-block mb-0">Ngx Datatables</h6>

          <nav
            aria-label="breadcrumb"
            class=" d-none d-md-inline-block ml-md-4"
          >
            <ol class=" breadcrumb breadcrumb-links breadcrumb-dark">
              <li class=" breadcrumb-item">
                <a href="javascript:void(0)"> <i class=" fas fa-home"> </i> </a>
              </li>

              <li class=" breadcrumb-item">
                <a href="javascript:void(0)"> Tables </a>
              </li>

              <li aria-current="page" class=" breadcrumb-item active">
                Ngx Datatables
              </li>
            </ol>
          </nav>
        </div>

        <div class=" col-lg-6 col-5 text-right">
          <a class=" btn btn-sm btn-neutral" href="javascript:void(0)"> New </a>

          <a class=" btn btn-sm btn-neutral" href="javascript:void(0)">
            Filters
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class=" container-fluid mt--6">
  <div class=" row">
    <div class=" col">
      <div class=" card">
        <div class=" card-header">
          <h3 class=" mb-0">Ngx Datatables</h3>

          <p class=" text-sm mb-0">
            This is an exmaple of datatable using the well known ngx-datatable
            plugin. This is a minimal setup in order to get started fast.
          </p>
        </div>
        <div class="dataTables_wrapper py-4">
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <div class="dataTables_length" id="datatable_length">
                <label>
                  Show
                  <select
                    name="datatable_length"
                    aria-controls="datatable"
                    class="form-control form-control-sm"
                    (change)="entriesChange($event)"
                  >
                    <option value="10" [selected]="entries == 10">10</option>
                    <option value="25" [selected]="entries == 25">25</option>
                    <option value="50" [selected]="entries == 50">50</option>
                    <option value="-1" [selected]="entries == -1">All</option>
                  </select>
                  entries
                </label>
              </div>
            </div>
            <div class="col-sm-12 col-md-6">
              <div id="datatable_filter" class="dataTables_filter">
                <label>
                  <input
                    type="search"
                    class="form-control form-control-sm"
                    placeholder="Search records"
                    aria-controls="datatable"
                    (keyup)="filterTable($event)"
                  />
                </label>
              </div>
            </div>
          </div>

          <ngx-datatable
            class="bootstrap selection-cell"
            [columnMode]="'force'"
            [headerHeight]="50"
            [footerHeight]="50"
            [rowHeight]="'auto'"
            [limit]="entries != -1 ? entries : undefined"
            [rows]="temp"
            (activate)="onActivate($event)"
          >
            <ngx-datatable-column name="Name"></ngx-datatable-column>
            <ngx-datatable-column name="Position"></ngx-datatable-column>
            <ngx-datatable-column name="Office"></ngx-datatable-column>
            <ngx-datatable-column name="Age"></ngx-datatable-column>
            <ngx-datatable-column name="Start"></ngx-datatable-column>
            <ngx-datatable-column name="Salary"></ngx-datatable-column>
          </ngx-datatable>
        </div>
      </div>
      <div class="card">
        <!-- Card header -->
        <div class="card-header">
          <h3 class="mb-0">Selecting Rows</h3>
          <p class="text-sm mb-0">
            This is an exmaple of datatable using the well known ngx-datatable
            plugin. This is a minimal setup in order to get started fast.
          </p>
        </div>
        <div class="dataTables_wrapper py-4">
          <div class="row">
            <div class="col-sm-12 col-md-6">
              <div class="dataTables_length" id="datatable_length">
                <label>
                  Show
                  <select
                    name="datatable_length"
                    aria-controls="datatable"
                    class="form-control form-control-sm"
                    (change)="entriesChange($event)"
                  >
                    <option value="10" [selected]="entries == 10">10</option>
                    <option value="25" [selected]="entries == 25">25</option>
                    <option value="50" [selected]="entries == 50">50</option>
                    <option value="-1" [selected]="entries == -1">All</option>
                  </select>
                  entries
                </label>
              </div>
            </div>
            <div class="col-sm-12 col-md-6">
              <div id="datatable_filter" class="dataTables_filter">
                <label>
                  <input
                    type="search"
                    class="form-control form-control-sm"
                    placeholder="Search records"
                    aria-controls="datatable"
                    (keyup)="filterTable($event)"
                  />
                </label>
              </div>
            </div>
          </div>

          <ngx-datatable
            id="print-section"
            class="bootstrap selection-cell"
            [columnMode]="'force'"
            [headerHeight]="50"
            [footerHeight]="50"
            [rowHeight]="'auto'"
            [limit]="entries != -1 ? entries : undefined"
            [rows]="temp"
            [selected]="selected"
            [selectionType]="'multiClick'"
            (activate)="onActivate($event)"
            (select)="onSelect($event)"
          >
            <ngx-datatable-column name="Name"></ngx-datatable-column>
            <ngx-datatable-column name="Position"></ngx-datatable-column>
            <ngx-datatable-column name="Office"></ngx-datatable-column>
            <ngx-datatable-column name="Age"></ngx-datatable-column>
            <ngx-datatable-column name="Start"></ngx-datatable-column>
            <ngx-datatable-column name="Salary"></ngx-datatable-column>
          </ngx-datatable>
        </div>
      </div>
    </div>
  </div>
</div>
