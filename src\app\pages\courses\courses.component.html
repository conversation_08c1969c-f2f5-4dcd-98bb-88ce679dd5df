<!-- Student-friendly Header -->
<div class="student-header mb-5">
  <div class="container-fluid">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-4 font-weight-bold text-dark mb-2">Discover Amazing Courses</h1>
        <p class="lead text-muted mb-0">Expand your knowledge with our expertly crafted courses designed for your success.</p>
      </div>
      <div class="col-lg-4 text-lg-right mt-3 mt-lg-0">
        <a href="#/my-courses" class="btn btn-primary btn-lg">
          <i class="ni ni-collection mr-2"></i>
          My Learning Journey
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="container-fluid">
  <div class="row">
    <div class="col">
      <!-- Filters Section -->
      <div class="card shadow mb-4">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-lg-6">
              <!-- Search Bar -->
              <div class="form-group mb-0">
                <div class="input-group input-group-alternative">
                  <div class="input-group-prepend">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                  </div>
                  <input
                    class="form-control"
                    placeholder="Search courses..."
                    type="text"
                    [(ngModel)]="searchTerm"
                    (input)="onSearchChange()"
                  />
                </div>
              </div>
            </div>
            <div class="col-lg-6">
              <!-- Category Filter -->
              <div class="form-group mb-0">
                <select
                  class="form-control"
                  [(ngModel)]="selectedCategory"
                  (change)="onCategoryChange(selectedCategory)"
                >
                  <option *ngFor="let category of categories" [value]="category">
                    {{ category }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Courses Grid -->
      <div class="row">
        <div class="col-xl-4 col-lg-6 col-md-6 mb-4" *ngFor="let course of filteredCourses">
          <div class="card course-card h-100 shadow">
            <!-- Course Image -->
            <div class="course-image-container">
              <img
                [src]="course.image"
                [alt]="course.title"
                class="card-img-top course-image"
                onerror="this.src='assets/img/theme/course-placeholder.jpg'"
              />
              <div class="course-overlay">
                <span class="badge badge-primary course-level">{{ course.level }}</span>
                <span class="badge badge-secondary course-category">{{ course.category }}</span>
              </div>
            </div>

            <div class="card-body d-flex flex-column">
              <!-- Course Title -->
              <h5 class="card-title font-weight-bold text-dark mb-2">{{ course.title }}</h5>

              <!-- Instructor -->
              <p class="text-muted mb-2">
                <i class="fas fa-user-tie mr-1"></i>
                {{ course.instructor }}
              </p>

              <!-- Description -->
              <p class="card-text text-sm mb-3">{{ course.description }}</p>

              <!-- Course Stats -->
              <div class="course-stats mb-3">
                <div class="row text-center">
                  <div class="col-4">
                    <div class="stat-item">
                      <i class="fas fa-clock text-primary"></i>
                      <small class="d-block text-muted">{{ course.duration }}</small>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="stat-item">
                      <i class="fas fa-users text-info"></i>
                      <small class="d-block text-muted">{{ course.studentsEnrolled }}</small>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="stat-item">
                      <div class="rating">
                        <span *ngFor="let star of getStarArray(course.rating)"
                              class="star"
                              [class.filled]="star">
                          <i class="fas fa-star"></i>
                        </span>
                        <small class="d-block text-muted">{{ course.rating }}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Progress Bar (if enrolled) -->
              <div *ngIf="course.isEnrolled && course.progress !== undefined" class="mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                  <small class="text-muted">Progress</small>
                  <small class="text-muted">{{ course.progress }}%</small>
                </div>
                <div class="progress progress-sm">
                  <div
                    class="progress-bar bg-primary"
                    role="progressbar"
                    [style.width.%]="course.progress"
                    [attr.aria-valuenow]="course.progress"
                    aria-valuemin="0"
                    aria-valuemax="100">
                  </div>
                </div>
              </div>

              <!-- Tags -->
              <div class="course-tags mb-3">
                <span *ngFor="let tag of course.tags" class="badge badge-light mr-1 mb-1">
                  {{ tag }}
                </span>
              </div>

              <!-- Price and Action Button -->
              <div class="mt-auto">
                <div class="d-flex justify-content-between align-items-center">
                  <div class="price">
                    <span class="h4 font-weight-bold text-primary">${{ course.price }}</span>
                  </div>
                  <div class="action-buttons">
                    <button
                      *ngIf="!course.isEnrolled"
                      class="btn btn-primary btn-sm"
                      (click)="enrollCourse(course)"
                    >
                      <i class="fas fa-plus mr-1"></i>
                      Enroll Now
                    </button>
                    <button
                      *ngIf="course.isEnrolled"
                      class="btn btn-success btn-sm"
                      (click)="continueCourse(course)"
                    >
                      <i class="fas fa-play mr-1"></i>
                      Continue
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Results Message -->
      <div *ngIf="filteredCourses.length === 0" class="row">
        <div class="col-12">
          <div class="card shadow">
            <div class="card-body text-center py-5">
              <i class="fas fa-search fa-3x text-muted mb-3"></i>
              <h4 class="text-muted">No courses found</h4>
              <p class="text-muted">Try adjusting your search criteria or browse different categories.</p>
              <button class="btn btn-primary" (click)="selectedCategory = 'All'; searchTerm = ''; filterCourses()">
                <i class="fas fa-refresh mr-1"></i>
                Reset Filters
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
