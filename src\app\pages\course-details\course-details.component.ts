import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Course } from 'src/app/core/models/Course';
import { CourseService } from 'src/app/core/services/course.service';

@Component({
  selector: 'app-course-details',
  templateUrl: './course-details.component.html',
  styleUrls: ['./course-details.component.scss']
})
export class CourseDetailsComponent implements OnInit {
  course: Course | null = null;
  isLoading: boolean = false;
  errorMessage: string = '';
  activeTab: string = 'overview';
  isEnrolled: boolean = false;
  progress: number = 0;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private courseService: CourseService
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const courseId = params['id'];
      if (courseId) {
        this.loadCourse(courseId);
      }
    });
  }

  loadCourse(courseId: number): void {
    this.isLoading = true;
    this.courseService.getCourseById(courseId).subscribe({
      next: (course) => {
        this.course = course;
        this.isLoading = false;
        // Check enrollment status (you can implement this with your enrollment API)
        this.checkEnrollmentStatus(courseId);
      },
      error: (error) => {
        console.error('Failed to load course:', error);
        this.errorMessage = 'Failed to load course details. Please try again.';
        this.isLoading = false;
      }
    });
  }

  checkEnrollmentStatus(courseId: number): void {
    // TODO: Implement enrollment check with your API
    // For now, using mock data
    this.isEnrolled = false;
    this.progress = 0;
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  enrollCourse(): void {
    if (!this.course) return;
    
    // TODO: Implement enrollment API call
    this.isEnrolled = true;
    this.progress = 0;
    console.log(`Enrolled in course: ${this.course.titre}`);
  }

  downloadFile(fileType: string): void {
    if (!this.course) return;
    
    let fileUrl = '';
    let fileName = '';
    
    switch (fileType) {
      case 'pdf':
        fileUrl = this.course.fichier || '';
        fileName = `${this.course.titre}_materials.pdf`;
        break;
      case 'video':
        fileUrl = this.course.video || '';
        fileName = `${this.course.titre}_video.mp4`;
        break;
    }
    
    if (fileUrl) {
      // Create download link
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      link.click();
    }
  }

  getStarArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= Math.floor(rating || 0));
    }
    return stars;
  }

  goBack(): void {
    this.router.navigate(['/courses']);
  }
}
