// Student-friendly layout styling with golden theme

.main-content {
  background: linear-gradient(135deg, #FFFBEB 0%, #FFF8E1 100%);
  min-height: 100vh;
}

// Student Navbar Styling
.student-navbar {
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
  box-shadow: 0 4px 20px rgba(28, 31, 33, 0.1);
  border: none;
  padding: 1rem 0;

  .navbar-brand {
    display: flex;
    align-items: center;
    color: #1C1F21 !important;
    font-size: 1.25rem;
    text-decoration: none;

    &:hover {
      color: #1C1F21 !important;
      text-decoration: none;
    }

    .navbar-brand-img {
      max-height: 2.5rem;
      width: auto;
      object-fit: contain;
    }
  }

  .navbar-nav {
    .nav-item {
      margin: 0 0.5rem;

      .nav-link {
        color: #1C1F21 !important;
        font-weight: 600;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
          color: #1C1F21 !important;
        }

        i {
          margin-right: 0.5rem;
          font-size: 1.1rem;
        }

        .nav-link-inner--text {
          font-size: 0.95rem;
        }
      }

      &.dropdown {
        .dropdown-menu {
          border: none;
          box-shadow: 0 10px 30px rgba(28, 31, 33, 0.15);
          border-radius: 12px;
          margin-top: 0.5rem;

          .dropdown-item {
            color: #1C1F21;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;

            &:hover {
              background-color: rgba(255, 204, 0, 0.1);
              color: #1C1F21;
            }

            i {
              margin-right: 0.75rem;
              color: #FFCC00;
            }
          }

          .dropdown-header {
            background-color: #FFEFAD;
            border-radius: 8px 8px 0 0;
            margin: -0.5rem -1rem 0.5rem -1rem;
            padding: 1rem 1.5rem;

            h6 {
              color: #1C1F21;
              font-weight: 700;
            }
          }

          .dropdown-divider {
            border-color: rgba(255, 204, 0, 0.3);
          }
        }
      }
    }
  }

  .navbar-toggler {
    border: 2px solid #1C1F21;
    border-radius: 8px;
    padding: 0.5rem;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(28, 31, 33, 0.25);
    }

    .navbar-toggler-icon {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2828, 31, 33, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    }
  }
}

// Avatar styling
.avatar {
  border: 2px solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;

  &:hover {
    border-color: #fff;
    transform: scale(1.05);
  }
}

// Page content styling
.container-fluid {
  padding: 2rem 1rem;

  @media (min-width: 768px) {
    padding: 2rem 3rem;
  }
}

// Student Footer
.student-footer {
  background: linear-gradient(135deg, #1C1F21 0%, #2C2F33 100%);
  color: #FFFBEB;
  margin-top: auto;

  .copyright {
    color: rgba(255, 251, 235, 0.8);

    a {
      color: #FFCC00;
      text-decoration: none;

      &:hover {
        color: #FFDA47;
        text-decoration: underline;
      }
    }
  }

  .nav-footer {
    .nav-item {
      .nav-link {
        color: rgba(255, 251, 235, 0.7);
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;

        &:hover {
          color: #FFCC00;
          transform: translateY(-1px);
        }
      }
    }
  }
}

// Mobile responsive adjustments
@media (max-width: 991px) {
  .student-navbar {
    .navbar-collapse {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 12px;
      margin-top: 1rem;
      padding: 1rem;
      box-shadow: 0 10px 30px rgba(28, 31, 33, 0.15);

      .navbar-nav {
        .nav-item {
          margin: 0.25rem 0;

          .nav-link {
            border-radius: 8px;
            margin: 0.25rem 0;
          }
        }
      }
    }

    .navbar-collapse-header {
      padding-bottom: 1rem;
      border-bottom: 1px solid rgba(255, 204, 0, 0.3);
      margin-bottom: 1rem;

      .collapse-brand img {
        max-height: 2rem;
      }

      .collapse-close {
        text-align: right;

        .navbar-toggler {
          background: none;
          border: none;
          padding: 0;

          span {
            display: block;
            width: 22px;
            height: 2px;
            background: #1C1F21;
            margin: 4px 0;
            transition: 0.3s;

            &:nth-child(1) {
              transform: rotate(-45deg) translate(-5px, 6px);
            }

            &:nth-child(2) {
              opacity: 0;
            }

            &:nth-child(3) {
              transform: rotate(45deg) translate(-5px, -6px);
            }
          }
        }
      }
    }
  }
}

// Smooth scrolling
html {
  scroll-behavior: smooth;
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #FFFBEB;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #FFDA47 0%, #FFCC00 100%);
}