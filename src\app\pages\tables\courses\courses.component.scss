.table-course {
  .media {
    .avatar {
      border-radius: 8px;
      border: 2px solid #e9ecef;
    }
  }
}

.table-actions {
  .btn {
    margin-right: 5px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

.modal {
  &.show {
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 15px 35px rgba(50, 50, 93, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
  }
  
  .modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem;
    
    .modal-title {
      font-weight: 600;
      color: #32325d;
    }
  }
  
  .modal-body {
    padding: 1.5rem;
  }
  
  .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
  }
}

.form-group {
  margin-bottom: 1.5rem;
  
  .form-control-label {
    font-weight: 600;
    color: #32325d;
    margin-bottom: 0.5rem;
  }
  
  .form-control {
    border-radius: 8px;
    border: 1px solid #cad1d7;
    padding: 0.75rem 1rem;
    
    &:focus {
      border-color: #FFCC00;
      box-shadow: 0 0 0 0.2rem rgba(255, 204, 0, 0.25);
    }
  }
  
  .form-control-file {
    padding: 0.5rem 0;
  }
}

.badge {
  font-size: 0.75rem;
  font-weight: 600;
  
  &.badge-pill {
    padding: 0.375rem 0.75rem;
  }
}

.btn {
  border-radius: 8px;
  font-weight: 600;
  
  &.btn-primary {
    background-color: #FFCC00;
    border-color: #FFCC00;
    color: #1C1F21;
    
    &:hover {
      background-color: #FFDA47;
      border-color: #FFDA47;
    }
    
    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(255, 204, 0, 0.25);
    }
  }
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.text-warning {
  color: #fb6340 !important;
}

.alert {
  border-radius: 8px;
  border: none;
  
  &.alert-danger {
    background-color: #f5365c;
    color: white;
  }
}

// Course image styling
.avatar {
  &.rounded {
    border-radius: 8px !important;
  }
}

// Empty state styling
.text-muted {
  .fa-graduation-cap {
    color: #adb5bd;
  }
  
  a {
    color: #FFCC00;
    text-decoration: none;
    
    &:hover {
      color: #FFDA47;
      text-decoration: underline;
    }
  }
}
