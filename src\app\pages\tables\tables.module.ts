import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { ProgressbarModule } from "ngx-bootstrap/progressbar";
import { BsDropdownModule } from "ngx-bootstrap/dropdown";
import { PaginationModule } from "ngx-bootstrap/pagination";
import { TooltipModule } from "ngx-bootstrap/tooltip";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { NgxPrintModule } from "ngx-print";

import { NgxDatatablesComponent } from "./ngxdatatables/ngxdatatables.component";
import { TablesComponent } from "./tables/tables.component";
import { SortableComponent } from "./sortable/sortable.component";
import { CoursesTableComponent } from "./courses/courses.component";

import { RouterModule } from "@angular/router";
import { TablesRoutes } from "./tables.routing";

@NgModule({
  declarations: [NgxDatatablesComponent, SortableComponent, TablesComponent, CoursesTableComponent],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule.forChild(TablesRoutes),
    NgxDatatableModule,
    ProgressbarModule.forRoot(),
    BsDropdownModule.forRoot(),
    PaginationModule.forRoot(),
    TooltipModule.forRoot(),
    NgxPrintModule
  ]
})
export class TablesModule {}
