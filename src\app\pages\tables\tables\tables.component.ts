import { HttpHeaders } from "@angular/common/http";
import { Component, OnInit } from "@angular/core";
import { User } from "src/app/core/models/User";
import { AuthService } from "src/app/core/services/auth-service.service";

@Component({
  selector: "app-tables",
  templateUrl: "tables.component.html"
})
export class TablesComponent implements OnInit {
  users: User[] = [];

  constructor(private userService: AuthService) {}

  ngOnInit(): void {
    this.fetchUsers();
  }
    

  fetchUsers(): void {
    this.userService.getUserList().subscribe({
      next: (data) => {
        this.users = data;
      },
      error: (err) => {
        console.error('Failed to load users', err);
      }
    });
  }
}
