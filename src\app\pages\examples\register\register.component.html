<div class=" main-content">
  <div class=" header bg-gradient-danger py-7 py-lg-8 pt-lg-9">
    <div class=" container">
      <div class=" header-body text-center mb-7">
        <div class=" row justify-content-center">
          <div class=" col-xl-5 col-lg-6 col-md-8 px-5">
            <h1 class=" text-white">Create an account</h1>

            <p class=" text-lead text-white">
              Welcome to be-nextGen learning plateforme
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="separator separator-bottom separator-skew zindex-100">
      <svg
        x="0"
        y="0"
        viewBox="0 0 2560 100"
        preserveAspectRatio="none"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <polygon class="fill-default" points="2560 0 2560 100 0 100"></polygon>
      </svg>
    </div>
  </div>

  <div class=" container mt--8 pb-5">
    <div class=" row justify-content-center">
      <div class=" col-lg-6 col-md-8">
        <div class=" card bg-secondary border-0">
          <div class=" card-header bg-transparent pb-5">
            <div class=" text-muted text-center mt-2 mb-4">
              <small> Sign up with </small>
            </div>

            <div class=" text-center">
              <a
                class=" btn btn-neutral btn-icon mr-4"
                href="javascript:void(0)"
              >
                <span class=" btn-inner--icon">
                  <img src="assets/img/icons/common/github.svg" />
                </span>

                <span class=" btn-inner--text"> Github </span>
              </a>

              <a class=" btn btn-neutral btn-icon" href="javascript:void(0)">
                <span class=" btn-inner--icon">
                  <img src="assets/img/icons/common/google.svg" />
                </span>

                <span class=" btn-inner--text"> Google </span>
              </a>
            </div>
          </div>

          <div class=" card-body px-lg-5 py-lg-5">
            <div class=" text-center text-muted mb-4">
              <small> Or sign up with credentials </small>
            </div>

            <form role="form">
              <!-- Image Upload Section -->
            <div class="relative flex justify-center">
              <img
                *ngIf="imagePreview"
                [src]="imagePreview"
                alt="Uploaded Image"
                id="uploadedImage"
                class="shadow-xl rounded-full h-auto border-none max-w-120-px"
              />
              <img
                *ngIf="!imagePreview"
                src="assets/img/theme/register_picture.png"
                alt="Default Image"
                id="uploadedImage"
                class="shadow-xl rounded-full h-auto border-none max-w-120-px"
              />
            </div>
                <div class="relative flex justify-center mt-3">
              <label for="imageUpload" class="text-blueGray-600 text-xs font-bold mb-2 cursor-pointer">
                Upload Image
              </label>
              <input
                id="imageUpload"
                type="file"
                class="hidden"
                accept="image/*"
                (change)="onImageUpload($event)"
              />
            </div>

              <div class="form-group" [ngClass]="{ focused: focus === true }">
                <div class="input-group input-group-alternative mb-3">
                  <div class="input-group-prepend">
                    <span class="input-group-text"
                      ><i class="ni ni-hat-3"></i
                    ></span>
                  </div>
                  <input
                    class="form-control"
                    placeholder="First Name"
                    type="text"
                    (focus)="focus = true"
                    (blur)="focus = false"
                  />
                </div>
              </div>
              <div class="form-group" [ngClass]="{ focused: focus1 === true }">
                <div class="input-group input-group-alternative mb-3">
                  <div class="input-group-prepend">
                    <span class="input-group-text"
                      ><i class="ni ni-hat-3"></i
                    ></span>
                  </div>
                  <input
                    class="form-control"
                    placeholder="Last Name"
                    type="text"
                    (focus)="focus1 = true"
                    (blur)="focus1 = false"
                  />
                </div>
              </div>

              <div class="form-group" [ngClass]="{ focused: focus2 === true }">
                <div class="input-group input-group-alternative mb-3">
                  <div class="input-group-prepend">
                    <span class="input-group-text"
                      ><i class="ni ni-email-83"></i
                    ></span>
                  </div>
                  <input
                    class="form-control"
                    placeholder="Email"
                    type="email"
                    (focus)="focus2 = true"
                    (blur)="focus2 = false"
                  />
                </div>
              </div>

              <div class="form-group" [ngClass]="{ focused: focus3 === true }">
                <div class="input-group input-group-alternative">
                  <div class="input-group-prepend">
                    <span class="input-group-text"
                      ><i class="ni ni-lock-circle-open"></i
                    ></span>
                  </div>
                  <input
                    class="form-control"
                    placeholder="Password"
                    type="password"
                    (focus)="focus3 = true"
                    (blur)="focus3 = false"
                  />
                </div>  
              </div>

                <div class="form-group" [ngClass]="{ focused: focus5 === true }">
                <div class="input-group input-group-alternative">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <i class="ni ni-circle-08"></i> <!-- icon for user role -->
                    </span>
                  </div>
                  <select
                    class="form-control"
                    [(ngModel)]="selectedRole"
                    name="role"
                    (focus)="focus5 = true"
                    (blur)="focus5 = false"
                  >
                    <option value="" disabled selected>Select Role</option>
                    <option value="TEACHER">Teacher</option>
                    <option value="STUDENT">Student</option>
                  </select>
                </div>
              </div>
              <div class="text-muted font-italic">
                <small
                  >password strength:
                  <span class="text-success font-weight-700">strong</span>
                </small>
              </div>
              <div class="row my-4">
                <div class="col-12">
                  <div
                    class="custom-control custom-control-alternative custom-checkbox"
                  >
                    <input
                      class="custom-control-input"
                      id="customCheckRegister"
                      type="checkbox"
                    />
                    <label
                      class="custom-control-label"
                      for="customCheckRegister"
                    >
                      <span
                        >I agree with the
                        <a href="javascript:void(0)">Privacy Policy</a>
                      </span>
                    </label>
                  </div>
                </div>
              </div>
              <div class="text-center">
                <button type="button" class="btn btn-primary mt-4">
                  Create account
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
