<div class=" main-content">
  <div class=" header bg-gradient-primary py-7 py-lg-8 pt-lg-9">
    <div class=" container">
      <div class=" header-body text-center mb-7">
        <div class=" row justify-content-center">
          <div class=" col-xl-5 col-lg-6 col-md-8 px-5">
            <h1 class=" text-dark font-weight-bold">Create an account</h1>

            <p class=" text-lead text-dark">
              Welcome to be-nextGen learning platform
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="separator separator-bottom separator-skew zindex-100">
      <svg
        x="0"
        y="0"
        viewBox="0 0 2560 100"
        preserveAspectRatio="none"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <polygon class="fill-default" points="2560 0 2560 100 0 100"></polygon>
      </svg>
    </div>
  </div>

  <div class=" container mt--8 pb-5">
    <div class=" row justify-content-center">
      <div class=" col-lg-6 col-md-8">
        <div class=" card bg-secondary border-0">
          
          <div class=" card-body px-lg-5 py-lg-5">
            <div class=" text-center text-muted mb-4">
              <small> Or sign up with credentials </small>
            </div>

            <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" role="form">
              <!-- Image Upload Section -->
            <div class="d-flex justify-content-center mb-4">
              <div class="profile-image-container position-relative"
                   title="Cliquez pour changer votre photo de profil"
                   data-toggle="tooltip"
                   data-placement="top">
                <img
                  *ngIf="imagePreview"
                  [src]="imagePreview"
                  alt="Photo de profil uploadée"
                  class="profile-image shadow-lg cursor-pointer"
                  (click)="triggerFileInput()"
                />
                <img
                  *ngIf="!imagePreview"
                  src="assets/img/theme/register_picture.png"
                  alt="Photo de profil par défaut"
                  class="profile-image shadow-lg cursor-pointer"
                  (click)="triggerFileInput()"
                />
                <div class="image-overlay" (click)="triggerFileInput()">
                  <i *ngIf="!isImageLoading" class="fas fa-camera text-white"></i>
                  <div *ngIf="isImageLoading" class="spinner-border spinner-border-sm text-white" role="status">
                    <span class="sr-only">Chargement...</span>
                  </div>
                </div>
                <input
                  #fileInput
                  type="file"
                  class="d-none"
                  accept="image/*"
                  (change)="onImageUpload($event)"
                  aria-label="Sélectionner une photo de profil"
                />
              </div>
            </div>
            <div class="text-center mb-3">
              <small class="text-muted">
                <i class="fas fa-info-circle mr-1"></i>
                Click on the image to select your profile picture
              </small>
            </div>

              <!-- First Name and Last Name on the same row -->
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group" [ngClass]="{ focused: focus === true }">
                    <div class="input-group input-group-alternative mb-3">
                      <div class="input-group-prepend">
                        <span class="input-group-text"
                          ><i class="ni ni-hat-3"></i
                        ></span>
                      </div>
                      <input
                        class="form-control"
                        placeholder="First Name"
                        type="text"
                        formControlName="prenom"
                        [class.is-invalid]="isFieldInvalid('prenom')"
                        (focus)="focus = true"
                        (blur)="focus = false"
                      />
                    </div>
                    <div *ngIf="isFieldInvalid('prenom')" class="text-danger">
                      <small>{{ getErrorMessage('prenom') }}</small>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group" [ngClass]="{ focused: focus1 === true }">
                    <div class="input-group input-group-alternative mb-3">
                      <div class="input-group-prepend">
                        <span class="input-group-text"
                          ><i class="ni ni-hat-3"></i
                        ></span>
                      </div>
                      <input
                        class="form-control"
                        placeholder="Last Name"
                        type="text"
                        formControlName="nom"
                        [class.is-invalid]="isFieldInvalid('nom')"
                        (focus)="focus1 = true"
                        (blur)="focus1 = false"
                      />
                    </div>
                    <div *ngIf="isFieldInvalid('nom')" class="text-danger">
                      <small>{{ getErrorMessage('nom') }}</small>
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-group" [ngClass]="{ focused: focus2 === true }">
                <div class="input-group input-group-alternative mb-3">
                  <div class="input-group-prepend">
                    <span class="input-group-text"
                      ><i class="ni ni-email-83"></i
                    ></span>
                  </div>
                  <input
                    class="form-control"
                    placeholder="Email"
                    type="email"
                    formControlName="email"
                    [class.is-invalid]="isFieldInvalid('email')"
                    (focus)="focus2 = true"
                    (blur)="focus2 = false"
                  />
                </div>
                <div *ngIf="isFieldInvalid('email')" class="text-danger">
                  <small>{{ getErrorMessage('email') }}</small>
                </div>
              </div>

              <div class="form-group" [ngClass]="{ focused: focus3 === true }">
                <div class="input-group input-group-alternative mb-3">
                  <div class="input-group-prepend">
                    <span class="input-group-text"
                      ><i class="ni ni-lock-circle-open"></i
                    ></span>
                  </div>
                  <input
                    class="form-control"
                    placeholder="Password"
                    type="password"
                    formControlName="password"
                    [class.is-invalid]="isFieldInvalid('password')"
                    (focus)="focus3 = true"
                    (blur)="focus3 = false"
                  />
                </div>
                <div *ngIf="isFieldInvalid('password')" class="text-danger">
                  <small>{{ getErrorMessage('password') }}</small>
                </div>
              </div>

              <div class="form-group" [ngClass]="{ focused: focus4 === true }">
                <div class="input-group input-group-alternative mb-3">
                  <div class="input-group-prepend">
                    <span class="input-group-text"
                      ><i class="ni ni-lock-circle-open"></i
                    ></span>
                  </div>
                  <input
                    class="form-control"
                    placeholder="Confirm Password"
                    type="password"
                    formControlName="confirmPassword"
                    [class.is-invalid]="isFieldInvalid('confirmPassword')"
                    (focus)="focus4 = true"
                    (blur)="focus4 = false"
                  />
                </div>
                <div *ngIf="isFieldInvalid('confirmPassword')" class="text-danger">
                  <small>{{ getErrorMessage('confirmPassword') }}</small>
                </div>
              </div>

                <div class="form-group" [ngClass]="{ focused: focus5 === true }">
                <div class="input-group input-group-alternative mb-3">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <i class="ni ni-circle-08"></i> <!-- icon for user role -->
                    </span>
                  </div>
                  <select
                    class="form-control"
                    formControlName="role"
                    [class.is-invalid]="isFieldInvalid('role')"
                    (focus)="focus5 = true"
                    (blur)="focus5 = false"
                  >
                    <option value="" disabled selected>Select Role</option>
                    <option value="TEACHER">Teacher</option>
                    <option value="STUDENT">Student</option>
                  </select>
                </div>
                <div *ngIf="isFieldInvalid('role')" class="text-danger">
                  <small>{{ getErrorMessage('role') }}</small>
                </div>
              </div>
              <div class="text-muted font-italic">
                <small
                  >password strength:
                  <span class="text-success font-weight-700">strong</span>
                </small>
              </div>

           

              <!-- Error/Success Messages -->
              <div *ngIf="errorMessage" class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                {{ errorMessage }}
              </div>

              <div *ngIf="successMessage" class="alert alert-success" role="alert">
                <i class="fas fa-check-circle mr-2"></i>
                {{ successMessage }}
              </div>

              <div class="text-center">
                

                <button
                  type="submit"
                  class="btn btn-primary mt-4"
                  [disabled]="registerForm.invalid || isLoading"
                >
                  <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span>
                  {{ isLoading ? 'Creating account...' : 'Create account' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
