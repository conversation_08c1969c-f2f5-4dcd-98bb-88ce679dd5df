// Custom navbar styles for golden theme
.navbar {
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
  border-bottom: 2px solid #1C1F21 !important;
  box-shadow: 0 2px 10px rgba(28, 31, 33, 0.1);

  // Text colors for better contrast on gold background
  .nav-link,
  .navbar-nav .nav-link {
    color: #1C1F21 !important;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      color: #000 !important;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 6px;
    }

    i {
      color: #1C1F21 !important;
    }
  }

  // Search form styling
  .navbar-search {
    .form-control {
      background-color: rgba(255, 255, 255, 0.9);
      border: 1px solid #1C1F21;
      color: #1C1F21;

      &::placeholder {
        color: rgba(28, 31, 33, 0.7);
      }

      &:focus {
        background-color: #fff;
        border-color: #1C1F21;
        box-shadow: 0 0 0 0.2rem rgba(28, 31, 33, 0.25);
      }
    }

    .input-group-text {
      background-color: rgba(255, 255, 255, 0.9);
      border: 1px solid #1C1F21;
      color: #1C1F21;
    }
  }

  // Dropdown menus
  .dropdown-menu {
    border: 1px solid #FFDA47;
    box-shadow: 0 4px 20px rgba(28, 31, 33, 0.15);

    .dropdown-item {
      color: #1C1F21;

      &:hover,
      &:focus {
        background-color: #FFEFAD;
        color: #1C1F21;
      }
    }

    .dropdown-header {
      color: #1C1F21;
    }
  }

  // User avatar and profile section
  .media-body {
    .text-sm {
      color: #1C1F21 !important;
    }
  }

  // Notification badge
  .badge {
    background-color: #1C1F21;
    color: #FFCC00;
  }

  // Sidenav toggler
  .sidenav-toggler {
    .sidenav-toggler-line {
      background-color: #1C1F21;
    }
  }
}

// Dropdown menu improvements
.dropdown-menu-xl {
  .list-group-item {
    border-color: #FFEFAD;

    &:hover {
      background-color: #FFFBEB;
    }
  }

  .text-primary {
    color: #FFCC00 !important;
  }
}

// Shortcuts styling
.shortcuts {
  .shortcut-item {
    color: #1C1F21;

    &:hover {
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }
  }

  .shortcut-media {
    &.bg-gradient-red { background: linear-gradient(45deg, #f5365c, #ff6b8a) !important; }
    &.bg-gradient-orange { background: linear-gradient(45deg, #fb6340, #ff8a65) !important; }
    &.bg-gradient-yellow { background: linear-gradient(45deg, #FFCC00, #FFDA47) !important; }
    &.bg-gradient-green { background: linear-gradient(45deg, #2dce89, #4caf50) !important; }
    &.bg-gradient-info { background: linear-gradient(45deg, #11cdef, #26c6da) !important; }
    &.bg-gradient-purple { background: linear-gradient(45deg, #8965e0, #ab47bc) !important; }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .navbar {
    .navbar-nav {
      background-color: rgba(255, 255, 255, 0.95);
      border-radius: 8px;
      margin-top: 10px;
      padding: 10px;
    }
  }
}