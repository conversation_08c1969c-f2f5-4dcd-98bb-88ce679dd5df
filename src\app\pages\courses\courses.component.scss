// Courses page styling with golden theme

.custom-golden-header {
  background: #FFCC00 !important;
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
  position: relative;
  overflow: hidden;

  // Remove any existing background images or patterns
  &::before,
  &::after {
    display: none !important;
  }

  // Ensure all text is dark for contrast
  h1, h2, h3, h4, h5, h6 {
    color: #1C1F21 !important;
    text-shadow: none !important;
    font-weight: 700 !important;
  }

  p, span, small, a {
    color: #1C1F21 !important;
    text-shadow: none !important;
  }

  // Breadcrumb styling
  .breadcrumb {
    background-color: transparent !important;

    .breadcrumb-item {
      a {
        color: #1C1F21 !important;

        &:hover {
          color: rgba(28, 31, 33, 0.8) !important;
        }
      }

      &.active {
        color: rgba(28, 31, 33, 0.7) !important;
      }
    }
  }
}

// Course card styling
.course-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 204, 0, 0.2) !important;
  background-color: #FFFBEB !important;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(28, 31, 33, 0.15) !important;
    border-color: #FFCC00 !important;
  }
}

// Course image container
.course-image-container {
  position: relative;
  overflow: hidden;
  height: 200px;

  .course-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .course-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;

    .badge {
      font-size: 0.75rem;
      padding: 0.375rem 0.75rem;
    }

    .course-level {
      background-color: #FFCC00 !important;
      color: #1C1F21 !important;
    }

    .course-category {
      background-color: rgba(28, 31, 33, 0.8) !important;
      color: #FFFBEB !important;
    }
  }

  &:hover .course-image {
    transform: scale(1.05);
  }
}

// Course stats styling
.course-stats {
  .stat-item {
    text-align: center;

    i {
      font-size: 1.2rem;
      margin-bottom: 0.25rem;
    }

    small {
      font-size: 0.75rem;
      font-weight: 600;
    }
  }
}

// Rating stars
.rating {
  .star {
    color: #e9ecef;
    font-size: 0.875rem;

    &.filled {
      color: #FFCC00;
    }
  }
}

// Progress bar styling
.progress {
  height: 6px;
  background-color: rgba(255, 204, 0, 0.2);

  .progress-bar {
    background: linear-gradient(90deg, #FFCC00 0%, #FFDA47 100%) !important;
  }
}

// Course tags
.course-tags {
  .badge-light {
    background-color: rgba(255, 239, 173, 0.5) !important;
    color: #1C1F21 !important;
    border: 1px solid rgba(255, 204, 0, 0.3);
    font-size: 0.7rem;
    font-weight: 500;
  }
}

// Price styling
.price {
  .h4 {
    margin-bottom: 0;
    color: #FFCC00 !important;
  }
}

// Action buttons
.action-buttons {
  .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;

    &.btn-primary {
      background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
      border-color: #FFCC00 !important;
      color: #1C1F21 !important;

      &:hover {
        background: linear-gradient(135deg, #FFDA47 0%, #FFCC00 100%) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 204, 0, 0.4);
      }
    }

    &.btn-success {
      background: linear-gradient(135deg, #2dce89 0%, #26c6da 100%) !important;
      border-color: #2dce89 !important;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(45, 206, 137, 0.4);
      }
    }
  }
}

// Search and filter section
.card {
  background-color: #FFFBEB !important;
  border: 1px solid rgba(255, 204, 0, 0.2) !important;

  .form-control {
    background-color: #fff !important;
    border: 2px solid rgba(255, 204, 0, 0.3) !important;
    color: #1C1F21 !important;

    &:focus {
      border-color: #FFCC00 !important;
      box-shadow: 0 0 0 0.2rem rgba(255, 204, 0, 0.25) !important;
      background-color: #fff !important;
    }

    &::placeholder {
      color: rgba(28, 31, 33, 0.6) !important;
    }
  }

  .input-group-text {
    background-color: #FFEFAD !important;
    border: 2px solid rgba(255, 204, 0, 0.3) !important;
    color: #1C1F21 !important;
  }
}

// No results section
.text-muted {
  color: rgba(28, 31, 33, 0.7) !important;
}

// Responsive adjustments
@media (max-width: 768px) {
  .course-image-container {
    height: 180px;
  }

  .course-stats {
    .row {
      text-align: center;
    }

    .stat-item {
      margin-bottom: 1rem;

      i {
        font-size: 1rem;
      }
    }
  }

  .action-buttons {
    .btn {
      font-size: 0.8rem;
      padding: 0.4rem 0.8rem;
    }
  }

  .price .h4 {
    font-size: 1.2rem;
  }
}

@media (max-width: 576px) {
  .course-image-container {
    height: 160px;
  }

  .course-card {
    margin-bottom: 1.5rem;
  }

  .header .row {
    text-align: center;

    .col-5 {
      margin-top: 1rem;
    }
  }
}