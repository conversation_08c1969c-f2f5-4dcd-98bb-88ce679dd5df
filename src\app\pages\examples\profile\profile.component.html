<div
  class=" header pb-6 d-flex align-items-center"
  style="min-height: 500px; background-image: url(assets/img/theme/profile-cover.jpg); background-size: cover; background-position: center top;"
>
  <span class=" mask bg-gradient-danger opacity-8"> </span>

  <div class=" container-fluid d-flex align-items-center">
    <div class=" row">
      <div class=" col-lg-7 col-md-10">
        <h1 class=" display-2 text-white">Hello Jesse</h1>

        <p class=" text-white mt-0 mb-5">
          This is your profile page. You can see the progress you've made with
          your work and manage your projects or assigned tasks
        </p>

        <a class=" btn btn-neutral" href="javascript:void(0)"> Edit profile </a>
      </div>
    </div>
  </div>
</div>

<div class=" container-fluid mt--6">
  <div class=" row">
    <div class=" col-xl-4 order-xl-2">
      <div class=" card card-profile">
        <img
          alt="Image placeholder"
          class=" card-img-top"
          src="assets/img/theme/img-1-1000x600.jpg"
        />

        <div class=" row justify-content-center">
          <div class=" col-lg-3 order-lg-2">
            <div class=" card-profile-image">
              <a href="javascript:void(0)">
                <img
                  class=" rounded-circle"
                  src="assets/img/theme/team-4.jpg"
                />
              </a>
            </div>
          </div>
        </div>

        <div
          class=" card-header text-center border-0 pt-8 pt-md-4 pb-0 pb-md-4"
        >
          <div class=" d-flex justify-content-between">
            <a class=" btn btn-sm btn-info mr-4" href="javascript:void(0)">
              Connect
            </a>

            <a
              class=" btn btn-sm btn-default float-right"
              href="javascript:void(0)"
            >
              Message
            </a>
          </div>
        </div>

        <div class=" card-body pt-0">
          <div class=" row">
            <div class=" col">
              <div class=" card-profile-stats d-flex justify-content-center">
                <div>
                  <span class=" heading"> 22 </span>

                  <span class=" description"> Friends </span>
                </div>

                <div>
                  <span class=" heading"> 10 </span>

                  <span class=" description"> Photos </span>
                </div>

                <div>
                  <span class=" heading"> 89 </span>

                  <span class=" description"> Comments </span>
                </div>
              </div>
            </div>
          </div>

          <div class=" text-center">
            <h5 class=" h3">
              Jessica Jones<span class=" font-weight-light"> , 27 </span>
            </h5>

            <div class=" h5 font-weight-300">
              <i class=" ni location_pin mr-2"> </i> Bucharest, Romania
            </div>

            <div class=" h5 mt-4">
              <i class=" ni business_briefcase-24 mr-2"> </i> Solution Manager -
              Creative Tim Officer
            </div>

            <div>
              <i class=" ni education_hat mr-2"> </i> University of Computer
              Science
            </div>
          </div>
        </div>
      </div>

      <div class=" card">
        <div class=" card-header"><h5 class=" h3 mb-0">Progress track</h5></div>

        <div class=" card-body">
          <ul class=" list-group list-group-flush list my--3">
            <li class=" list-group-item px-0">
              <div class=" row align-items-center">
                <div class=" col-auto">
                  <a class=" avatar rounded-circle" href="javascript:void(0)">
                    <img
                      alt="Image placeholder"
                      src="assets/img/theme/bootstrap.jpg"
                    />
                  </a>
                </div>

                <div class=" col">
                  <h5>Argon Design System</h5>

                  <div class=" progress progress-xs mb-0">
                    <progressbar type="warning" [value]="60"> </progressbar>
                  </div>
                </div>
              </div>
            </li>

            <li class=" list-group-item px-0">
              <div class=" row align-items-center">
                <div class=" col-auto">
                  <a class=" avatar rounded-circle" href="javascript:void(0)">
                    <img
                      alt="Image placeholder"
                      src="assets/img/theme/angular.jpg"
                    />
                  </a>
                </div>

                <div class=" col">
                  <h5>Angular Now UI Kit PRO</h5>

                  <div class=" progress progress-xs mb-0">
                    <progressbar type="success" [value]="100"> </progressbar>
                  </div>
                </div>
              </div>
            </li>

            <li class=" list-group-item px-0">
              <div class=" row align-items-center">
                <div class=" col-auto">
                  <a class=" avatar rounded-circle" href="javascript:void(0)">
                    <img
                      alt="Image placeholder"
                      src="assets/img/theme/sketch.jpg"
                    />
                  </a>
                </div>

                <div class=" col">
                  <h5>Black Dashboard</h5>

                  <div class=" progress progress-xs mb-0">
                    <progressbar type="danger" [value]="72"> </progressbar>
                  </div>
                </div>
              </div>
            </li>

            <li class=" list-group-item px-0">
              <div class=" row align-items-center">
                <div class=" col-auto">
                  <a class=" avatar rounded-circle" href="javascript:void(0)">
                    <img
                      alt="Image placeholder"
                      src="assets/img/theme/react.jpg"
                    />
                  </a>
                </div>

                <div class=" col">
                  <h5>React Material Dashboard</h5>

                  <div class=" progress progress-xs mb-0">
                    <progressbar type="info" [value]="90"> </progressbar>
                  </div>
                </div>
              </div>
            </li>

            <li class=" list-group-item px-0">
              <div class=" row align-items-center">
                <div class=" col-auto">
                  <a class=" avatar rounded-circle" href="javascript:void(0)">
                    <img
                      alt="Image placeholder"
                      src="assets/img/theme/vue.jpg"
                    />
                  </a>
                </div>

                <div class=" col">
                  <h5>Vue Paper UI Kit PRO</h5>

                  <div class=" progress progress-xs mb-0">
                    <progressbar type="success" [value]="100"> </progressbar>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class=" col-xl-8 order-xl-1">
      <div class=" row">
        <div class=" col-lg-6">
          <div class=" card bg-gradient-info border-0">
            <div class=" card-body">
              <div class=" row">
                <div class=" col">
                  <h5
                    class=" card-title text-uppercase text-muted mb-0 text-white"
                  >
                    Total traffic
                  </h5>

                  <span class=" h2 font-weight-bold mb-0 text-white">
                    350,897
                  </span>
                </div>

                <div class=" col-auto">
                  <div
                    class=" icon icon-shape bg-white text-dark rounded-circle shadow"
                  >
                    <i class=" ni ni-active-40"> </i>
                  </div>
                </div>
              </div>

              <p class=" mt-3 mb-0 text-sm">
                <span class=" text-white mr-2">
                  <i class=" fa fa-arrow-up"> </i> 3.48%
                </span>

                <span class=" text-nowrap text-light"> Since last month </span>
              </p>
            </div>
          </div>
        </div>

        <div class=" col-lg-6">
          <div class=" card bg-gradient-success border-0">
            <div class=" card-body">
              <div class=" row">
                <div class=" col">
                  <h5
                    class=" card-title text-uppercase text-muted mb-0 text-white"
                  >
                    Performance
                  </h5>

                  <span class=" h2 font-weight-bold mb-0 text-white">
                    49,65%
                  </span>
                </div>

                <div class=" col-auto">
                  <div
                    class=" icon icon-shape bg-white text-dark rounded-circle shadow"
                  >
                    <i class=" ni ni-spaceship"> </i>
                  </div>
                </div>
              </div>

              <p class=" mt-3 mb-0 text-sm">
                <span class=" text-white mr-2">
                  <i class=" fa fa-arrow-up"> </i> 3.48%
                </span>

                <span class=" text-nowrap text-light"> Since last month </span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <div class=" card">
        <div class=" card-header">
          <div class=" row align-items-center">
            <div class=" col-8"><h3 class=" mb-0">Edit profile</h3></div>

            <div class=" col-4 text-right">
              <a class=" btn btn-sm btn-primary" href="javascript:void(0)">
                Settings
              </a>
            </div>
          </div>
        </div>

        <div class=" card-body">
          <form>
            <h6 class=" heading-small text-muted mb-4">User information</h6>

            <div class=" pl-lg-4">
              <div class=" row">
                <div class=" col-lg-6">
                  <div class=" form-group">
                    <label class=" form-control-label" for="input-username">
                      Username
                    </label>

                    <input
                      class=" form-control"
                      id="input-username"
                      placeholder="Username"
                      type="text"
                      value="lucky.jesse"
                    />
                  </div>
                </div>

                <div class=" col-lg-6">
                  <div class=" form-group">
                    <label class=" form-control-label" for="input-email">
                      Email address
                    </label>

                    <input
                      class=" form-control"
                      id="input-email"
                      placeholder="<EMAIL>"
                      type="email"
                    />
                  </div>
                </div>
              </div>

              <div class=" row">
                <div class=" col-lg-6">
                  <div class=" form-group">
                    <label class=" form-control-label" for="input-first-name">
                      First name
                    </label>

                    <input
                      class=" form-control"
                      id="input-first-name"
                      placeholder="First name"
                      type="text"
                      value="Lucky"
                    />
                  </div>
                </div>

                <div class=" col-lg-6">
                  <div class=" form-group">
                    <label class=" form-control-label" for="input-last-name">
                      Last name
                    </label>

                    <input
                      class=" form-control"
                      id="input-last-name"
                      placeholder="Last name"
                      type="text"
                      value="Jesse"
                    />
                  </div>
                </div>
              </div>
            </div>

            <hr class=" my-4" />

            <h6 class=" heading-small text-muted mb-4">Contact information</h6>

            <div class=" pl-lg-4">
              <div class=" row">
                <div class=" col-md-12">
                  <div class=" form-group">
                    <label class=" form-control-label" for="input-address">
                      Address
                    </label>

                    <input
                      class=" form-control"
                      id="input-address"
                      placeholder="Home Address"
                      type="text"
                      value="Bld Mihail Kogalniceanu, nr. 8 Bl 1, Sc 1, Ap 09"
                    />
                  </div>
                </div>
              </div>

              <div class=" row">
                <div class=" col-lg-4">
                  <div class=" form-group">
                    <label class=" form-control-label" for="input-city">
                      City
                    </label>

                    <input
                      class=" form-control"
                      id="input-city"
                      placeholder="City"
                      type="text"
                      value="New York"
                    />
                  </div>
                </div>

                <div class=" col-lg-4">
                  <div class=" form-group">
                    <label class=" form-control-label" for="input-country">
                      Country
                    </label>

                    <input
                      class=" form-control"
                      id="input-country"
                      placeholder="Country"
                      type="text"
                      value="United States"
                    />
                  </div>
                </div>

                <div class=" col-lg-4">
                  <div class=" form-group">
                    <label class=" form-control-label" for="input-country">
                      Postal code
                    </label>

                    <input
                      class=" form-control"
                      id="input-postal-code"
                      placeholder="Postal code"
                      type="number"
                    />
                  </div>
                </div>
              </div>
            </div>

            <hr class=" my-4" />

            <h6 class=" heading-small text-muted mb-4">About me</h6>

            <div class=" pl-lg-4">
              <div class=" form-group">
                <label class=" form-control-label"> About Me </label>

                <textarea
                  class=" form-control"
                  placeholder="A few words about you ..."
                  rows="4"
                >
A beautiful premium dashboard for Bootstrap 4.
</textarea
                >
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
