# CORS Configuration for Keycloak Backend

## Problem
The frontend application running on `http://localhost:4201` cannot access the Keycloak server on `http://localhost:8081` due to CORS (Cross-Origin Resource Sharing) restrictions.

## ✅ CURRENT SOLUTION IMPLEMENTED
The application is now running with a development proxy that forwards API requests to avoid CORS issues:
- Frontend: `http://localhost:4201`
- Proxy handles: `/api/*` → `http://localhost:8081`
- Proxy handles: `/backend/*` → `http://localhost:8050`

## Alternative Solutions (if proxy doesn't work)
You can configure CORS on your Keycloak server to allow requests from your Angular frontend.

### Option 1: Keycloak Admin Console Configuration
1. Open Keycloak Admin Console: `http://localhost:8081/admin`
2. Navigate to your realm: `BNG_Elearning_realm`
3. Go to `Clients` → `BNG-Elearning-rest-api`
4. In the `Settings` tab, add the following:
   - **Valid Redirect URIs**: `http://localhost:4201/*`
   - **Web Origins**: `http://localhost:4201`

### Option 2: Keycloak Configuration File
Add the following to your Keycloak configuration:

```json
{
  "clientId": "BNG-Elearning-rest-api",
  "webOrigins": ["http://localhost:4201"],
  "redirectUris": ["http://localhost:4201/*"]
}
```

### Option 3: Development Proxy (Alternative)
If you cannot modify the backend, you can use the Angular development proxy:

1. The `proxy.conf.json` file is already created
2. Start the Angular development server with: `ng serve --proxy-config proxy.conf.json`

### Verification
After configuring CORS, the authentication should work without the CORS error.

## Additional Notes
- Make sure your Keycloak server is running on port 8081
- The client configuration must match exactly
- For production, update the origins to match your production domain
