<div class=" header bg-danger pb-6">
  <div class=" container-fluid">
    <div class=" header-body">
      <div class=" row align-items-center py-4">
        <div class=" col-lg-6 col-7">
          <h6 class=" h2 text-white d-inline-block mb-0">Tables</h6>

          <nav
            aria-label="breadcrumb"
            class=" d-none d-md-inline-block ml-md-4"
          >
            <ol class=" breadcrumb breadcrumb-links breadcrumb-dark">
              <li class=" breadcrumb-item">
                <a href="javascript:void(0)"> <i class=" fas fa-home"> </i> </a>
              </li>

              <li class=" breadcrumb-item">
                <a href="javascript:void(0)"> Tables </a>
              </li>

              <li aria-current="page" class=" breadcrumb-item active">
                Tables
              </li>
            </ol>
          </nav>
        </div>

        <div class=" col-lg-6 col-5 text-right">
          <a class=" btn btn-sm btn-neutral" href="javascript:void(0)"> New </a>

          <a class=" btn btn-sm btn-neutral" href="javascript:void(0)">
            Filters
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class=" container-fluid mt--6">


  <div class=" card">
    <div class=" card-header border-0">
      <div class=" row">
        <div class=" col-6"><h3 class=" mb-0">User table</h3></div>

        <div class=" col-6 text-right">
          <a
            class=" btn btn-sm btn-primary btn-round btn-icon"
            href="javascript:void(0)"
            tooltip="Edit product"
            placement="top"
          >
            <span class=" btn-inner--icon">
              <i class=" fas fa-user-edit"> </i>
            </span>

            <span class=" btn-inner--text"> Export </span>
          </a>
        </div>
      </div>
    </div>

    <div class=" table-responsive">
      <table class=" table align-items-center table-flush table-striped">
        <thead class=" thead-light">
          <tr>
            <th>User</th>

            <th>Created at</th>

            <th>Role</th>

            <th></th>
          </tr>
        </thead>

       <tbody>
  <tr *ngFor="let user of users">
    <td class="table-user">
      <img
        class="avatar rounded-circle mr-3"
        [src]="user.image || 'assets/img/theme/team-1.jpg'"
        alt="User avatar"
      />
      <b>{{ user.nom }} {{ user.prenom }}</b>
    </td>

    <td>
      <span class="text-muted">{{ user.email }}</span>
    </td>

    <td>
      <span class="font-weight-bold text-primary">{{ user.role }}</span>
    </td>

    <td class="table-actions">
      <a
        class="table-action"
        tooltip="Edit user"
        placement="top"
        href="javascript:void(0)"
      >
        <i class="fas fa-user-edit"></i>
      </a>

      <a
        class="table-action table-action-delete"
        tooltip="Delete user"
        placement="top"
        href="javascript:void(0)"
      >
        <i class="fas fa-trash"></i>
      </a>
    </td>
  </tr>
</tbody>

      </table>
    </div>
  </div>

</div>
