{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"argon-dashboard-pro-angular": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "node_modules/fullcalendar/main.min.css", "src/assets/vendor/nucleo/css/nucleo.css", "src/assets/vendor/@fortawesome/fontawesome-free/css/all.min.css", "node_modules/ngx-bootstrap/datepicker/bs-datepicker.css", "node_modules/mobius1-selectr/dist/selectr.min.css", "node_modules/@swimlane/ngx-datatable/index.css", "node_modules/@swimlane/ngx-datatable/assets/icons.css", "src/assets/scss/argon.scss", "node_modules/@swimlane/ngx-datatable/themes/bootstrap.css"], "scripts": ["node_modules/chart.js/dist/Chart.min.js"], "allowedCommonJsDependencies": ["chart.js", "list.js", "node_modules/devextreme/dist/js/vectormap-data/world.js", "quill", "moment", "devexpress-diagram", "devexpress-gantt", "devextreme-quill", "j<PERSON><PERSON>"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2.5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "4kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "argon-dashboard-pro-angular:build", "proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"browserTarget": "argon-dashboard-pro-angular:build:production"}, "development": {"browserTarget": "argon-dashboard-pro-angular:build:development"}}, "defaultConfiguration": "production"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "argon-dashboard-pro-angular:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss"], "scripts": [], "assets": ["src/favicon.ico", "src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "argon-dashboard-pro-angular-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "argon-dashboard-pro-angular:serve"}, "configurations": {"production": {"devServerTarget": "argon-dashboard-pro-angular:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "argon-dashboard-pro-angular", "cli": {"analytics": false}}