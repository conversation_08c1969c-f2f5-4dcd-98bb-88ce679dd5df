<!-- Header -->
<div class="header bg-primary pb-6">
  <div class="container-fluid">
    <div class="header-body">
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 text-white d-inline-block mb-0">Courses Management</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
              <li class="breadcrumb-item"><a href="#"><i class="fas fa-home"></i></a></li>
              <li class="breadcrumb-item"><a href="#">Tables</a></li>
              <li class="breadcrumb-item active" aria-current="page">Courses</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right">
          <button class="btn btn-sm btn-neutral" (click)="openAddModal()">
            <i class="fas fa-plus mr-2"></i>New Course
          </button>
          <button class="btn btn-sm btn-neutral" (click)="loadCourses()">
            <i class="fas fa-sync mr-2"></i>Refresh
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Page content -->
<div class="container-fluid mt--6" style="background-color: #FFFBEB !important;">
  <!-- Error message -->
  <div *ngIf="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ errorMessage }}
    <button type="button" class="close" (click)="errorMessage = ''" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <!-- Courses table -->
  <div class="card">
    <div class="card-header border-0">
      <div class="row">
        <div class="col-6">
          <h3 class="mb-0">Courses Table</h3>
        </div>
        <div class="col-6 text-right">
          <button class="btn btn-sm btn-primary btn-round btn-icon" (click)="openAddModal()">
            <span class="btn-inner--icon">
              <i class="fas fa-plus"></i>
            </span>
            <span class="btn-inner--text">Add Course</span>
          </button>
        </div>
      </div>
    </div>

    <div class="table-responsive">
      <table class="table align-items-center table-flush table-striped">
        <thead class="thead-light">
          <tr>
            <th>Course</th>
            <th>Instructor</th>
            <th>Category</th>
            <th>Level</th>
            <th>Price</th>
            <th>Status</th>
            <th>Students</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="isLoading">
            <td colspan="8" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </td>
          </tr>
          <tr *ngFor="let course of courses" [class.table-warning]="course.status === 'DRAFT'">
            <td class="table-course">
              <div class="media align-items-center">
                <img 
                  class="avatar rounded mr-3" 
                  [src]="course.image || 'assets/img/theme/course-default.jpg'" 
                  alt="Course image"
                  style="width: 50px; height: 50px; object-fit: cover;"
                />
                <div class="media-body">
                  <span class="font-weight-bold">{{ course.title }}</span>
                  <br>
                  <small class="text-muted">{{ course.duration }}</small>
                </div>
              </div>
            </td>
            <td>
              <span class="text-dark">{{ course.instructor }}</span>
            </td>
            <td>
              <span class="badge badge-pill badge-primary">{{ course.category }}</span>
            </td>
            <td>
              <span class="badge badge-pill" [ngClass]="getLevelBadgeClass(course.level)">
                {{ course.level }}
              </span>
            </td>
            <td>
              <span class="font-weight-bold text-success">{{ formatPrice(course.price) }}</span>
            </td>
            <td>
              <span class="badge badge-pill" [ngClass]="getStatusBadgeClass(course.status)">
                {{ course.status }}
              </span>
            </td>
            <td>
              <span class="text-muted">
                {{ course.enrolled_students || 0 }} / {{ course.max_students || 'Unlimited' }}
              </span>
            </td>
            <td class="table-actions">
              <button 
                class="btn btn-sm btn-outline-primary mr-2" 
                (click)="openEditModal(course)"
                title="Edit course">
                <i class="fas fa-edit"></i>
              </button>
              <button 
                class="btn btn-sm btn-outline-danger" 
                (click)="openDeleteModal(course)"
                title="Delete course">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
          <tr *ngIf="!isLoading && courses.length === 0">
            <td colspan="8" class="text-center py-4">
              <div class="text-muted">
                <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                <p>No courses found. <a href="javascript:void(0)" (click)="openAddModal()">Add your first course</a></p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Add Course Modal -->
<div class="modal fade" [class.show]="showAddModal" [style.display]="showAddModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="addCourseModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addCourseModalLabel">Add New Course</h5>
        <button type="button" class="close" (click)="closeModals()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-control-label">Course Title *</label>
                <input type="text" class="form-control" [(ngModel)]="courseForm.title"
                       name="title" placeholder="Enter course title" required>
              </div>
            </div>
            
          </div>
          <div class="form-group">
            <label class="form-control-label">Description *</label>
            <textarea class="form-control" [(ngModel)]="courseForm.enrolled"
                      name="description" rows="3" placeholder="Enter course description" required></textarea>
          </div>

          <div class="form-group">
            <label class="form-control-label">Description *</label>
            <textarea class="form-control" [(ngModel)]="courseForm.description"
                      name="description" rows="3" placeholder="Enter course description" required></textarea>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Duration *</label>
                <input type="text" class="form-control" [(ngModel)]="courseForm.duration"
                       name="duration" placeholder="e.g., 8 weeks" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Level</label>
                <select class="form-control" [(ngModel)]="courseForm.level" name="level">
                  <option *ngFor="let level of levels" [value]="level">{{ level }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Category *</label>
                <select class="form-control" [(ngModel)]="courseForm.category" name="category" required>
                  <option value="">Select category</option>
                  <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Price ($)</label>
                <input type="number" class="form-control" [(ngModel)]="courseForm.price"
                       name="price" min="0" step="0.01" placeholder="0.00">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Max Students</label>
                <input type="number" class="form-control" [(ngModel)]="courseForm.maxStudents"
                       name="maxStudents" min="1" placeholder="50">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Status</label>
                <select class="form-control" [(ngModel)]="courseForm.status" name="status">
                  <option *ngFor="let status of statuses" [value]="status">{{ status }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-control-label">Course Image</label>
            <input type="file" class="form-control-file" (change)="onImageSelected($event)"
                   accept="image/*">
            <small class="form-text text-muted">Upload an image for the course (optional)</small>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeModals()">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="addCourse()"
                [disabled]="!validateForm() || isLoading">
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2"></span>
          Add Course
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Course Modal -->
<div class="modal fade" [class.show]="showEditModal" [style.display]="showEditModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="editCourseModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editCourseModalLabel">Edit Course</h5>
        <button type="button" class="close" (click)="closeModals()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-control-label">Course Title *</label>
                <input type="text" class="form-control" [(ngModel)]="courseForm.title"
                       name="title" placeholder="Enter course title" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-control-label">Instructor *</label>
                <input type="text" class="form-control" [(ngModel)]="courseForm.instructor"
                       name="instructor" placeholder="Enter instructor name" required>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-control-label">Description *</label>
            <textarea class="form-control" [(ngModel)]="courseForm.description"
                      name="description" rows="3" placeholder="Enter course description" required></textarea>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Duration *</label>
                <input type="text" class="form-control" [(ngModel)]="courseForm.duration"
                       name="duration" placeholder="e.g., 8 weeks" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Level</label>
                <select class="form-control" [(ngModel)]="courseForm.level" name="level">
                  <option *ngFor="let level of levels" [value]="level">{{ level }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Category *</label>
                <select class="form-control" [(ngModel)]="courseForm.category" name="category" required>
                  <option value="">Select category</option>
                  <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Price ($)</label>
                <input type="number" class="form-control" [(ngModel)]="courseForm.price"
                       name="price" min="0" step="0.01" placeholder="0.00">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Max Students</label>
                <input type="number" class="form-control" [(ngModel)]="courseForm.maxStudents"
                       name="maxStudents" min="1" placeholder="50">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Status</label>
                <select class="form-control" [(ngModel)]="courseForm.status" name="status">
                  <option *ngFor="let status of statuses" [value]="status">{{ status }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-control-label">Course Image</label>
            <input type="file" class="form-control-file" (change)="onImageSelected($event)"
                   accept="image/*">
            <small class="form-text text-muted">Upload a new image to replace the current one (optional)</small>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeModals()">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="updateCourse()"
                [disabled]="!validateForm() || isLoading">
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2"></span>
          Update Course
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Delete Course Modal -->
<div class="modal fade" [class.show]="showDeleteModal" [style.display]="showDeleteModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="deleteCourseModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteCourseModalLabel">Delete Course</h5>
        <button type="button" class="close" (click)="closeModals()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="text-center">
          <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
          <h4>Are you sure?</h4>
          <p class="text-muted">
            You are about to delete the course "<strong>{{ selectedCourse?.titre }}</strong>".
            This action cannot be undone.
          </p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeModals()">Cancel</button>
        <button type="button" class="btn btn-danger" (click)="deleteCourse()" [disabled]="isLoading">
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2"></span>
          Delete Course
        </button>
      </div>
    </div>
  </div>
</div>
