<!-- Header -->
<div class="header bg-primary pb-6">
  <div class="container-fluid">
    <div class="header-body">
      <div class="row align-items-center py-4">
        <div class="col-lg-6 col-7">
          <h6 class="h2 text-white d-inline-block mb-0">Courses Management</h6>
          <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
            <ol class="breadcrumb breadcrumb-links breadcrumb-dark">
              <li class="breadcrumb-item"><a href="#"><i class="fas fa-home"></i></a></li>
              <li class="breadcrumb-item"><a href="#">Tables</a></li>
              <li class="breadcrumb-item active" aria-current="page">Courses</li>
            </ol>
          </nav>
        </div>
        <div class="col-lg-6 col-5 text-right">
          <button class="btn btn-sm btn-neutral" (click)="openAddModal()">
            <i class="fas fa-plus mr-2"></i>New Course
          </button>
          <button class="btn btn-sm btn-info" (click)="openCategoryModal()">
            <i class="fas fa-tags mr-2"></i>Manage Categories
          </button>
          <button class="btn btn-sm btn-neutral" (click)="loadCourses()">
            <i class="fas fa-sync mr-2"></i>Refresh
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Page content -->
<div class="container-fluid mt--6" style="background-color: #FFFBEB !important;">
  <!-- Error message -->
  <div *ngIf="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ errorMessage }}
    <button type="button" class="close" (click)="errorMessage = ''" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <!-- Courses table -->
  <div class="card">
    <div class="card-header border-0">
      <div class="row">
        <div class="col-6">
          <h3 class="mb-0">Courses Table</h3>
        </div>
        <div class="col-6 text-right">
          <button class="btn btn-sm btn-primary btn-round btn-icon" (click)="openAddModal()">
            <span class="btn-inner--icon">
              <i class="fas fa-plus"></i>
            </span>
            <span class="btn-inner--text">Add Course</span>
          </button>
        </div>
      </div>
    </div>

    <div class="table-responsive">
      <table class="table align-items-center table-flush table-striped">
        <thead class="thead-light">
          <tr>
            <th>Course</th>
            <th>Category</th>
            <th>Level</th>
            <th>Publish Date</th>
            <th>Price</th>
            <th>Rating</th>
            <th>Students</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="isLoading">
            <td colspan="8" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </td>
          </tr>
          <tr *ngFor="let course of courses">
            <td class="table-course">
              <div class="media align-items-center">
                <img
                  class="avatar rounded mr-3"
                  [src]="course.image || 'assets/img/theme/course-default.jpg'"
                  alt="Course image"
                  style="width: 50px; height: 50px; object-fit: cover;"
                />
                <div class="media-body">
                  <span class="font-weight-bold">{{ course.titre }}</span>
                  <br>
                  <small class="text-muted">{{ course.duree }} Month(s)</small>
                </div>
              </div>
            </td>
            <td>
              <span class="badge badge-pill badge-primary">{{ course.categorie }}</span>
            </td>
            <td>
              <span class="badge badge-pill" [ngClass]="getLevelBadgeClass(course.niveau)">
                {{ course.niveau }}
              </span>
            </td>
            <td>
             <span class="badge badge-pill badge-info">
                {{ course.publication_date ? (course.publication_date | date: 'dd/MM/yyyy') : (today | date: 'dd/MM/yyyy') }}
              </span>
            </td>
            <td>
              <span class="font-weight-bold text-success">{{ formatPrice(course.prix) }}</span>
            </td>
            <td>
              <div class="d-flex align-items-center">
                <span class="text-warning mr-1">
                  <i class="fas fa-star" *ngFor="let star of [1,2,3,4,5]; let i = index"
                     [class.text-muted]="i >= (course.rating || 0)"></i>
                </span>
                <small class="text-muted">({{ course.rating || 0 }})</small>
              </div>
            </td>
            <td>
              <span class="text-muted">
                {{ course.nb_etudiants_enrolled || 0 }} / {{ course.nb_max_etudiant || 'Unlimited' }}
              </span>
            </td>
            <td class="table-actions">
              <button
                class="btn btn-sm btn-outline-primary mr-2"
                (click)="openEditModal(course)"
                title="Edit course">
                <i class="fas fa-edit"></i>
              </button>
              <button
                class="btn btn-sm btn-outline-danger"
                (click)="openDeleteModal(course)"
                title="Delete course">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
          <tr *ngIf="!isLoading && courses.length === 0">
            <td colspan="8" class="text-center py-4">
              <div class="text-muted">
                <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                <p>No courses found. <a href="javascript:void(0)" (click)="openAddModal()">Add your first course</a></p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Add Course Modal -->
<div class="modal fade" [class.show]="showAddModal" [style.display]="showAddModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="addCourseModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addCourseModalLabel">Add New Course</h5>
        <button type="button" class="close" (click)="closeModals()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="form-control-label">Course Title *</label>
                <input type="text" class="form-control" [(ngModel)]="courseForm.titre"
                       name="titre" placeholder="Enter course title" required>
              </div>
            </div>
            
          </div>
       

          <div class="form-group">
            <label class="form-control-label">Description *</label>
            <textarea class="form-control" [(ngModel)]="courseForm.description"
                      name="description" rows="3" placeholder="Enter course description" required></textarea>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Duration *</label>
                <input type="text" class="form-control" [(ngModel)]="courseForm.duree"
                       name="duree" placeholder="e.g., 8 weeks" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Level</label>
                <select class="form-control" [(ngModel)]="courseForm.niveau" name="niveau">
                  <option *ngFor="let level of levels" [value]="level">{{ level }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Category *</label>
                <select class="form-control" [(ngModel)]="courseForm.categorie" name="categorie" required>
                  <option value="">Select category</option>
                  <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Price ($)</label>
                <input type="number" class="form-control" [(ngModel)]="courseForm.prix"
                       name="prix" min="0" step="0.01" placeholder="0.00">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Max Students</label>
                <input type="number" class="form-control" [(ngModel)]="courseForm.nbMaxEtudiant"
                       name="nbMaxEtudiant" min="1" placeholder="50" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Format</label>
                <select class="form-control" [(ngModel)]="courseForm.format" name="format">
                  <option *ngFor="let format of formats" [value]="format">{{ format }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Course Image</label>
                <input type="file" class="form-control-file" (change)="onImageSelected($event)"
                       accept="image/*">
                <small class="form-text text-muted">Upload course image</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Course Video</label>
                <input type="file" class="form-control-file" (change)="onVideoSelected($event)"
                       accept="video/*">
                <small class="form-text text-muted">Upload course video</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Course Files</label>
                <input type="file" class="form-control-file" (change)="onFileSelected($event)"
                       accept=".pdf,.doc,.docx,.ppt,.pptx">
                <small class="form-text text-muted">Upload course materials</small>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeModals()">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="addCourse()"
                [disabled]="!validateForm() || isLoading">
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2"></span>
          Add Course
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Course Modal -->
<div class="modal fade" [class.show]="showEditModal" [style.display]="showEditModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="editCourseModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editCourseModalLabel">Edit Course</h5>
        <button type="button" class="close" (click)="closeModals()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label class="form-control-label">Course Title *</label>
                <input type="text" class="form-control" [(ngModel)]="courseForm.titre"
                       name="titre" placeholder="Enter course title" required>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-control-label">Description *</label>
            <textarea class="form-control" [(ngModel)]="courseForm.description"
                      name="description" rows="3" placeholder="Enter course description" required></textarea>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Duration *</label>
                <input type="text" class="form-control" [(ngModel)]="courseForm.duree"
                       name="duree" placeholder="e.g., 8 weeks" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Level</label>
                <select class="form-control" [(ngModel)]="courseForm.niveau" name="niveau">
                  <option *ngFor="let level of levels" [value]="level">{{ level }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Category *</label>
                <select class="form-control" [(ngModel)]="courseForm.categorie" name="categorie" required>
                  <option value="">Select category</option>
                  <option *ngFor="let category of categories" [value]="category">{{ category }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Price ($)</label>
                <input type="number" class="form-control" [(ngModel)]="courseForm.prix"
                       name="prix" min="0" step="0.01" placeholder="0.00">
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Max Students</label>
                <input type="number" class="form-control" [(ngModel)]="courseForm.nbMaxEtudiant"
                       name="nbMaxEtudiant" min="1" placeholder="50" required>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Format</label>
                <select class="form-control" [(ngModel)]="courseForm.format" name="format">
                  <option *ngFor="let format of formats" [value]="format">{{ format }}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Course Image</label>
                <input type="file" class="form-control-file" (change)="onImageSelected($event)"
                       accept="image/*">
                <small class="form-text text-muted">Upload new image</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Course Video</label>
                <input type="file" class="form-control-file" (change)="onVideoSelected($event)"
                       accept="video/*">
                <small class="form-text text-muted">Upload new video</small>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label class="form-control-label">Course Files</label>
                <input type="file" class="form-control-file" (change)="onFileSelected($event)"
                       accept=".pdf,.doc,.docx,.ppt,.pptx">
                <small class="form-text text-muted">Upload new materials</small>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeModals()">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="updateCourse()"
                [disabled]="!validateForm() || isLoading">
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2"></span>
          Update Course
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Delete Course Modal -->
<div class="modal fade" [class.show]="showDeleteModal" [style.display]="showDeleteModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="deleteCourseModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteCourseModalLabel">Delete Course</h5>
        <button type="button" class="close" (click)="closeModals()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="text-center">
          <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
          <h4>Are you sure?</h4>
          <p class="text-muted">
            You are about to delete the course "<strong>{{ selectedCourse?.titre }}</strong>".
            This action cannot be undone.
          </p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeModals()">Cancel</button>
        <button type="button" class="btn btn-danger" (click)="deleteCourse()" [disabled]="isLoading">
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2"></span>
          Delete Course
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Category Management Modal -->
<div class="modal fade" [class.show]="showCategoryModal" [style.display]="showCategoryModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="categoryModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="categoryModalLabel">Category Management</h5>
        <button type="button" class="close" (click)="closeCategoryModals()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- Add New Category Form -->
        <div class="card mb-4">
          <div class="card-header">
            <h6 class="mb-0">Add New Category</h6>
          </div>
          <div class="card-body">
            <form>
              <div class="row">
                <div class="col-md-8">
                  <div class="form-group">
                    <label class="form-control-label">Category Title *</label>
                    <input type="text" class="form-control" [(ngModel)]="categoryForm.titre"
                           name="categoryTitle" placeholder="Enter category title" required>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label class="form-control-label">&nbsp;</label>
                    <button type="button" class="btn btn-primary btn-block" (click)="addCategory()"
                            [disabled]="!categoryForm.titre.trim() || isLoading">
                      <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2"></span>
                      <i class="fas fa-plus mr-1"></i>
                      Add Category
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

        <!-- Categories List -->
        <div class="card">
          <div class="card-header">
            <h6 class="mb-0">Existing Categories</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table align-items-center table-flush">
                <thead class="thead-light">
                  <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let category of categoriesList">
                    <td>{{ category.id }}</td>
                    <td>{{ category.title }}</td>
                    <td>
                      <button class="btn btn-sm btn-outline-primary mr-2"
                              (click)="openEditCategoryModal(category)" title="Edit category">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger"
                              (click)="openDeleteCategoryModal(category)" title="Delete category">
                        <i class="fas fa-trash"></i>
                      </button>
                    </td>
                  </tr>
                  <tr *ngIf="categoriesList.length === 0">
                    <td colspan="3" class="text-center text-muted">No categories found</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeCategoryModals()">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" [class.show]="showEditCategoryModal" [style.display]="showEditCategoryModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editCategoryModalLabel">Edit Category</h5>
        <button type="button" class="close" (click)="closeCategoryModals()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="form-group">
            <label class="form-control-label">Category Title *</label>
            <input type="text" class="form-control" [(ngModel)]="categoryForm.titre"
                   name="editCategoryTitle" placeholder="Enter category title" required>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeCategoryModals()">Cancel</button>
        <button type="button" class="btn btn-primary" (click)="updateCategory()"
                [disabled]="!categoryForm.titre.trim() || isLoading">
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2"></span>
          Update Category
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" [class.show]="showDeleteCategoryModal" [style.display]="showDeleteCategoryModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteCategoryModalLabel">Delete Category</h5>
        <button type="button" class="close" (click)="closeCategoryModals()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="text-center">
          <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
          <h4>Are you sure?</h4>
          <p class="text-muted">
            You are about to delete the category "<strong>{{ selectedCategory?.titre }}</strong>".
            This action cannot be undone.
          </p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeCategoryModals()">Cancel</button>
        <button type="button" class="btn btn-danger" (click)="deleteCategory()" [disabled]="isLoading">
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm mr-2"></span>
          Delete Category
        </button>
      </div>
    </div>
  </div>
</div>
