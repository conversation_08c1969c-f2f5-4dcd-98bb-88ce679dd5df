<div class=" header bg-danger pb-6">
  <div class=" container-fluid">
    <div class=" header-body">
      <div class=" row align-items-center py-4">
        <div class=" col-lg-6 col-7">
          <h6 class=" h2 text-white d-inline-block mb-0">Form elements</h6>

          <nav
            aria-label="breadcrumb"
            class=" d-none d-md-inline-block ml-md-4"
          >
            <ol class=" breadcrumb breadcrumb-links breadcrumb-dark">
              <li class=" breadcrumb-item">
                <a href="javascript:void(0)"> <i class=" fas fa-home"> </i> </a>
              </li>

              <li class=" breadcrumb-item">
                <a href="javascript:void(0)"> Forms </a>
              </li>

              <li aria-current="page" class=" breadcrumb-item active">
                Form elements
              </li>
            </ol>
          </nav>
        </div>

        <div class=" col-lg-6 col-5 text-right">
          <a class=" btn btn-sm btn-neutral" href="javascript:void(0)"> New </a>

          <a class=" btn btn-sm btn-neutral" href="javascript:void(0)">
            Filters
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class=" container-fluid mt--6">
  <div class=" card mb-4">
    <div class=" card-header"><h3 class=" mb-0">Form group in grid</h3></div>

    <div class=" card-body">
      <div class=" row">
        <div class=" col-md-4">
          <div class=" form-group">
            <label class=" form-control-label" for="example3cols1Input">
              One of three cols
            </label>

            <input
              class=" form-control"
              id="example3cols1Input"
              placeholder="One of three cols"
              type="text"
            />
          </div>
        </div>

        <div class=" col-md-4">
          <div class=" form-group">
            <label class=" form-control-label" for="example3cols2Input">
              One of three cols
            </label>

            <input
              class=" form-control"
              id="example3cols2Input"
              placeholder="One of three cols"
              type="text"
            />
          </div>
        </div>

        <div class=" col-md-4">
          <div class=" form-group">
            <label class=" form-control-label" for="example3cols3Input">
              One of three cols
            </label>

            <input
              class=" form-control"
              id="example3cols3Input"
              placeholder="One of three cols"
              type="text"
            />
          </div>
        </div>
      </div>

      <div class=" row">
        <div class=" col-sm-6 col-md-3">
          <div class=" form-group">
            <label class=" form-control-label" for="example4cols1Input">
              One of four cols
            </label>

            <input
              class=" form-control"
              id="example4cols1Input"
              placeholder="One of four cols"
              type="text"
            />
          </div>
        </div>

        <div class=" col-sm-6 col-md-3">
          <div class=" form-group">
            <label class=" form-control-label" for="example4cols2Input">
              One of four cols
            </label>

            <input
              class=" form-control"
              id="example4cols2Input"
              placeholder="One of four cols"
              type="text"
            />
          </div>
        </div>

        <div class=" col-sm-6 col-md-3">
          <div class=" form-group">
            <label class=" form-control-label" for="example4cols3Input">
              One of four cols
            </label>

            <input
              class=" form-control"
              id="example4cols3Input"
              placeholder="One of four cols"
              type="text"
            />
          </div>
        </div>

        <div class=" col-sm-6 col-md-3">
          <div class=" form-group">
            <label class=" form-control-label" for="example4cols3Input">
              One of four cols
            </label>

            <input
              class=" form-control"
              id="example4cols4Input"
              placeholder="One of four cols"
              type="text"
            />
          </div>
        </div>
      </div>

      <div class=" row">
        <div class=" col-md-6">
          <div class=" form-group">
            <label class=" form-control-label" for="example2cols1Input">
              One of two cols
            </label>

            <input
              class=" form-control"
              id="example2cols1Input"
              placeholder="One of two cols"
              type="text"
            />
          </div>
        </div>

        <div class=" col-md-6">
          <div class=" form-group">
            <label class=" form-control-label" for="example2cols2Input">
              One of two cols
            </label>

            <input
              class=" form-control"
              id="example2cols2Input"
              placeholder="One of two cols"
              type="text"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class=" row">
    <div class=" col-lg-6">
      <div class=" card-wrapper">
        <div class=" card">
          <div class=" card-header"><h3 class=" mb-0">Form controls</h3></div>

          <div class=" card-body">
            <form>
              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlInput1"
                >
                  Email address
                </label>

                <input
                  class=" form-control"
                  id="exampleFormControlInput1"
                  placeholder="<EMAIL>"
                  type="email"
                />
              </div>

              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlSelect1"
                >
                  Example select
                </label>

                <select class=" form-control" id="exampleFormControlSelect1">
                  <option> 1 </option>

                  <option> 2 </option>

                  <option> 3 </option>

                  <option> 4 </option>

                  <option> 5 </option>
                </select>
              </div>

              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlSelect2"
                >
                  Example multiple select
                </label>

                <select
                  class=" form-control"
                  id="exampleFormControlSelect2"
                  multiple="multiple"
                >
                  <option> 1 </option>

                  <option> 2 </option>

                  <option> 3 </option>

                  <option> 4 </option>

                  <option> 5 </option>
                </select>
              </div>

              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlTextarea1"
                >
                  Example textarea
                </label>

                <textarea
                  class=" form-control"
                  id="exampleFormControlTextarea1"
                  rows="3"
                ></textarea>
              </div>
            </form>
          </div>
        </div>

        <div class=" card">
          <div class=" card-header"><h3 class=" mb-0">HTML5 inputs</h3></div>

          <div class=" card-body">
            <form>
              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-text-input"
                >
                  Text
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-text-input"
                    type="text"
                    value="John Snow"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-search-input"
                >
                  Search
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-search-input"
                    type="search"
                    value="Tell me your secret ..."
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-email-input"
                >
                  Email
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-email-input"
                    type="email"
                    value="<EMAIL>"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-url-input"
                >
                  URL
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-url-input"
                    type="url"
                    value="https://www.creative-tim.com"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-tel-input"
                >
                  Phone
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-tel-input"
                    type="tel"
                    value="40-(770)-888-444"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-password-input"
                >
                  Password
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-password-input"
                    type="password"
                    value="password"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-number-input"
                >
                  Number
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-number-input"
                    type="number"
                    value="23"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-datetime-local-input"
                >
                  Datetime
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-datetime-local-input"
                    type="datetime-local"
                    value="2018-11-23T10:30:00"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-date-input"
                >
                  Date
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-date-input"
                    type="date"
                    value="2018-11-23"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-month-input"
                >
                  Month
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-month-input"
                    type="month"
                    value="2018-11"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-week-input"
                >
                  Week
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-week-input"
                    type="week"
                    value="2018-W23"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-time-input"
                >
                  Time
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-time-input"
                    type="time"
                    value="10:30:00"
                  />
                </div>
              </div>

              <div class=" form-group row">
                <label
                  class=" col-md-2 col-form-label form-control-label"
                  for="example-color-input"
                >
                  Color
                </label>

                <div class=" col-md-10">
                  <input
                    class=" form-control"
                    id="example-color-input"
                    type="color"
                    value="#5e72e4"
                  />
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <div class=" col-lg-6">
      <div class=" card-wrapper">
        <div class=" card">
          <div class=" card-header"><h3 class=" mb-0">Sizes</h3></div>

          <div class=" card-body">
            <div class=" form-group">
              <label class=" form-control-label"> Large input </label>

              <input
                class=" form-control form-control-lg"
                placeholder=".form-control-lg"
                type="text"
              />
            </div>

            <div class=" form-group">
              <label class=" form-control-label"> Default input </label>

              <input
                class=" form-control"
                placeholder="Default input"
                type="text"
              />
            </div>

            <div class=" form-group">
              <label class=" form-control-label"> Small input </label>

              <input
                class=" form-control form-control-sm"
                placeholder=".form-control-sm"
                type="text"
              />
            </div>
          </div>
        </div>

        <div class=" card">
          <div class=" card-header"><h3 class=" mb-0">Text inputs</h3></div>

          <div class=" card-body">
            <form>
              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlTextarea1"
                >
                  Basic textarea
                </label>

                <textarea
                  class=" form-control"
                  id="exampleFormControlTextarea3"
                  rows="3"
                ></textarea>
              </div>

              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlTextarea2"
                >
                  Unresizable textarea
                </label>

                <textarea
                  class=" form-control"
                  id="exampleFormControlTextarea4"
                  resize="none"
                  rows="3"
                ></textarea>
              </div>
            </form>
          </div>
        </div>

        <div class=" card">
          <div class=" card-header"><h3 class=" mb-0">Select</h3></div>

          <div class=" card-body">
            <form>
              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlSelect3"
                >
                  Basic select
                </label>

                <select class=" form-control" id="exampleFormControlSelect3">
                  <option> 1 </option>

                  <option> 2 </option>

                  <option> 3 </option>

                  <option> 4 </option>

                  <option> 5 </option>
                </select>
              </div>

              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlSelect4"
                >
                  Basic select
                </label>

                <select
                  class=" form-control"
                  disabled="disabled"
                  id="exampleFormControlSelect4"
                >
                  <option> 1 </option>

                  <option> 2 </option>

                  <option> 3 </option>

                  <option> 4 </option>

                  <option> 5 </option>
                </select>
              </div>

              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlSelect5"
                >
                  Multiple select
                </label>

                <select
                  class=" form-control"
                  id="exampleFormControlSelect5"
                  multiple="multiple"
                >
                  <option> 1 </option>

                  <option> 2 </option>

                  <option> 3 </option>

                  <option> 4 </option>

                  <option> 5 </option>
                </select>
              </div>

              <div class=" form-group">
                <label
                  class=" form-control-label"
                  for="exampleFormControlSelect6"
                >
                  Disabled multiple select
                </label>

                <select
                  class=" form-control"
                  disabled="disabled"
                  id="exampleFormControlSelect6"
                  multiple="multiple"
                >
                  <option> 1 </option>

                  <option> 2 </option>

                  <option> 3 </option>

                  <option> 4 </option>

                  <option> 5 </option>
                </select>
              </div>
            </form>
          </div>
        </div>

        <div class=" card">
          <div class=" card-header"><h3 class=" mb-0">File browser</h3></div>

          <div class=" card-body">
            <form>
              <div class=" custom-file">
                <input
                  class=" custom-file-input"
                  id="customFileLang"
                  lang="en"
                  type="file"
                />

                <label class=" custom-file-label" for="customFileLang">
                  Select file
                </label>
              </div>
            </form>
          </div>
        </div>

        <div class=" card">
          <div class=" card-header">
            <h3 class=" mb-0">Checkboxes and radios</h3>
          </div>

          <div class=" card-body">
            <form>
              <div class=" row">
                <div class=" col-md-6">
                  <div class=" custom-control custom-checkbox mb-3">
                    <input
                      class=" custom-control-input"
                      id="customCheck1"
                      type="checkbox"
                    />

                    <label class=" custom-control-label" for="customCheck1">
                      Unchecked
                    </label>
                  </div>

                  <div class=" custom-control custom-checkbox mb-3">
                    <input
                      checked="checked"
                      class=" custom-control-input"
                      id="customCheck2"
                      type="checkbox"
                    />

                    <label class=" custom-control-label" for="customCheck2">
                      Checked
                    </label>
                  </div>

                  <div class=" custom-control custom-checkbox mb-3">
                    <input
                      class=" custom-control-input"
                      disabled="disabled"
                      id="customCheck3"
                      type="checkbox"
                    />

                    <label class=" custom-control-label" for="customCheck3">
                      Disabled Unchecked
                    </label>
                  </div>

                  <div class=" custom-control custom-checkbox mb-3">
                    <input
                      checked="checked"
                      class=" custom-control-input"
                      disabled="disabled"
                      id="customCheck4"
                      type="checkbox"
                    />

                    <label class=" custom-control-label" for="customCheck4">
                      Disabled Checked
                    </label>
                  </div>
                </div>

                <div class=" col-md-6">
                  <div class=" custom-control custom-radio mb-3">
                    <input
                      class=" custom-control-input"
                      id="customRadio5"
                      name="custom-radio-1"
                      type="radio"
                    />

                    <label class=" custom-control-label" for="customRadio5">
                      Unchecked
                    </label>
                  </div>

                  <div class=" custom-control custom-radio mb-3">
                    <input
                      checked=""
                      class=" custom-control-input"
                      id="customRadio6"
                      name="custom-radio-1"
                      type="radio"
                    />

                    <label class=" custom-control-label" for="customRadio6">
                      Checked
                    </label>
                  </div>

                  <div class=" custom-control custom-radio mb-3">
                    <input
                      class=" custom-control-input"
                      disabled=""
                      id="customRadio7"
                      name="custom-radio-3"
                      type="radio"
                    />

                    <label class=" custom-control-label" for="customRadio7">
                      Disabled unchecked
                    </label>
                  </div>

                  <div class=" custom-control custom-radio mb-3">
                    <input
                      checked=""
                      class=" custom-control-input"
                      disabled=""
                      id="customRadio8"
                      name="custom-radio-4"
                      type="radio"
                    />

                    <label class=" custom-control-label" for="customRadio8">
                      Disabled checkbox
                    </label>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
