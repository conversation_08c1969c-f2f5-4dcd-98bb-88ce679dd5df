import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-unauthorized',
  template: `
    <div class="min-height-300 bg-primary position-absolute w-100"></div>
    <div class="main-content">
      <div class="container mt-7">
        <div class="row justify-content-center">
          <div class="col-lg-5 col-md-7">
            <div class="card bg-secondary border-0 mb-0">
              <div class="card-body px-lg-5 py-lg-5">
                <div class="text-center text-muted mb-4">
                  <h1 class="display-1 text-warning">403</h1>
                  <h3>Access Denied</h3>
                  <p class="lead">You don't have permission to access this page.</p>
                </div>
                <div class="text-center">
                  <button class="btn btn-primary" (click)="goBack()">
                    <i class="ni ni-bold-left mr-2"></i>
                    Go Back
                  </button>
                  <button class="btn btn-outline-primary ml-2" (click)="goHome()">
                    <i class="ni ni-shop mr-2"></i>
                    Home
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .min-height-300 {
      min-height: 300px;
    }
    .main-content {
      position: relative;
      z-index: 1;
    }
  `]
})
export class UnauthorizedComponent implements OnInit {

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  goBack(): void {
    window.history.back();
  }

  goHome(): void {
    // Determine home based on user role
    const userData = localStorage.getItem('userData');
    if (userData) {
      const user = JSON.parse(userData);
      if (user.role === 'STUDENT') {
        this.router.navigate(['/courses']);
      } else {
        this.router.navigate(['/dashboards/dashboard']);
      }
    } else {
      this.router.navigate(['/examples/login']);
    }
  }
}
