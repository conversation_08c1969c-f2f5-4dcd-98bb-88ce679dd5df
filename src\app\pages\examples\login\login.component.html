<div class=" main-content">
  <div class=" header bg-gradient-danger py-7 py-lg-8 pt-lg-9">
    <div class=" container">
      <div class=" header-body text-center mb-7">
        <div class=" row justify-content-center">
          <div class=" col-xl-5 col-lg-6 col-md-8 px-5">
            <h1 class=" text-white">Welcome!</h1>

            <p class=" text-lead text-white">
              Use these awesome forms to login or create new account in your
              project for free.
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="separator separator-bottom separator-skew zindex-100">
      <svg
        x="0"
        y="0"
        viewBox="0 0 2560 100"
        preserveAspectRatio="none"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <polygon class="fill-default" points="2560 0 2560 100 0 100"></polygon>
      </svg>
    </div>
  </div>

  <div class=" container mt--8 pb-5">
    <div class=" row justify-content-center">
      <div class=" col-lg-5 col-md-7">
        <div class=" card bg-secondary border-0 mb-0">
          <div class=" card-header bg-transparent pb-5">
            <div class=" text-muted text-center mt-2 mb-3">
              <small> Sign in with </small>
            </div>

            <div class=" btn-wrapper text-center">
              <a class=" btn btn-neutral btn-icon" href="javascript:void(0)">
                <span class=" btn-inner--icon">
                  <img src="assets/img/icons/common/github.svg" />
                </span>

                <span class=" btn-inner--text"> Github </span>
              </a>

              <a class=" btn btn-neutral btn-icon" href="javascript:void(0)">
                <span class=" btn-inner--icon">
                  <img src="assets/img/icons/common/google.svg" />
                </span>

                <span class=" btn-inner--text"> Google </span>
              </a>
            </div>
          </div>

          <div class=" card-body px-lg-5 py-lg-5">
            <div class=" text-center text-muted mb-4">
              <small> Or sign in with credentials </small>
            </div>

          <form [formGroup]="authForm" (ngSubmit)="submit()">
  <div class="form-group mb-3" [ngClass]="{ focused: focus === true }">
    <div class="input-group input-group-alternative">
      <div class="input-group-prepend">
        <span class="input-group-text"><i class="ni ni-email-83"></i></span>
      </div>
      <input
        formControlName="email"
        class="form-control"
        placeholder="Email"
        type="email"
        (focus)="focus = true"
        (blur)="focus = false"
      />
    </div>
    <div *ngIf="isFieldInvalid('email')" class="text-danger">
      {{ getErrorMessage('email') }}
    </div>
  </div>

  <div class="form-group" [ngClass]="{ focused: focus1 === true }">
    <div class="input-group input-group-alternative">
      <div class="input-group-prepend">
        <span class="input-group-text"
          ><i class="ni ni-lock-circle-open"></i
        ></span>
      </div>
      <input
        formControlName="mdp"
        class="form-control"
        placeholder="Password"
        type="password"
        (focus)="focus1 = true"
        (blur)="focus1 = false"
      />
    </div>
    <div *ngIf="isFieldInvalid('mdp')" class="text-danger">
      {{ getErrorMessage('mdp') }}
    </div>
  </div>

  <div class="form-group mb-4">
    <div class="d-flex justify-content-center">
      <re-captcha
        formControlName="recaptcha"
        siteKey="6LdcKBcrAAAAAKlbQnx0CYKU4DaUorY6KcqlCqeT"
        (resolved)="onCaptchaResolved($event)"
        (expired)="onCaptchaExpired()"
        (error)="onCaptchaError($event)">
      </re-captcha>
    </div>
    <div *ngIf="isFieldInvalid('recaptcha')" class="text-danger text-center mt-2">
      <small>{{ getErrorMessage('recaptcha') }}</small>
    </div>
  </div>

  <div class="text-center">
    <button
      type="submit"
      class="btn btn-primary my-4"
      [disabled]="authForm.invalid || isLoading"
    >
      <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
      {{ isLoading ? 'Connexion...' : 'Se connecter' }}
    </button>
    <div *ngIf="errorMessage" class="text-danger mt-2">
      {{ errorMessage }}
    </div>
  </div>
</form>

          </div>
        </div>

        <div class=" row mt-3">
          <div class=" col-6">
            <a class=" text-light" href="javascript:void(0)">
              <small> Forgot password? </small>
            </a>
          </div>

          <div class=" col-6 text-right">
            <a class=" text-light" href="javascript:void(0)">
              <small> Create new account </small>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

