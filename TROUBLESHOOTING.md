# Authentication Troubleshooting Guide

## Current Status
✅ **Frontend**: Running on `http://localhost:4201` with proxy configuration
✅ **reCAPTCHA**: Properly integrated and styled
✅ **Form Validation**: Working with proper error messages
✅ **Route Guards**: Implemented for authentication protection

## 🚨 **Current Issue: 404 Not Found**

The authentication endpoint `http://localhost:8081/realms/BNG_Elearning_realm/protocol/openid-connect/token` is returning 404.

### **Possible Causes & Solutions:**

### 1. **Keycloak Server Not Running**
**Check if Keycloak is running:**
```bash
# Test if Keycloak server is accessible
curl http://localhost:8081
# OR
Invoke-WebRequest -Uri "http://localhost:8081" -Method Get
```

**Solution:** Start your Keycloak server on port 8081

### 2. **Incorrect Realm Name**
**Current configuration:** `BNG_Elearning_realm`

**Verify realm exists:**
- Open Keycloak Admin Console: `http://localhost:8081/admin`
- Check if realm `BNG_Elearning_realm` exists
- Verify the exact spelling and case sensitivity

### 3. **Incorrect Client Configuration**
**Current client ID:** `BNG-Elearning-rest-api`

**Verify in Keycloak Admin Console:**
- Navigate to Realm → Clients
- Check if client `BNG-Elearning-rest-api` exists
- Verify client is enabled
- Check Access Type is set to "public" or "confidential" as needed

### 4. **Wrong Keycloak Version/Configuration**
**Current endpoint:** `/protocol/openid-connect/token`

**For older Keycloak versions, try:**
```
/auth/realms/BNG_Elearning_realm/protocol/openid-connect/token
```

### 5. **Port Conflict**
**Verify Keycloak is running on port 8081:**
```bash
netstat -an | findstr :8081
```

## **Quick Fixes to Try:**

### **Option 1: Update Endpoint with /auth prefix**
If using older Keycloak version, update the auth service:

```typescript
// In auth-service.service.ts
const url = '/api/auth/realms/BNG_Elearning_realm/protocol/openid-connect/token';
```

### **Option 2: Test Direct Connection (Bypass Proxy)**
Temporarily test without proxy by updating auth service:

```typescript
// In auth-service.service.ts
const url = 'http://localhost:8081/realms/BNG_Elearning_realm/protocol/openid-connect/token';
```

### **Option 3: Verify Keycloak Configuration**
1. Access Keycloak Admin Console: `http://localhost:8081/admin`
2. Login with admin credentials
3. Select your realm: `BNG_Elearning_realm`
4. Go to Clients → `BNG-Elearning-rest-api`
5. Verify settings:
   - Client ID: `BNG-Elearning-rest-api`
   - Access Type: `public`
   - Valid Redirect URIs: `http://localhost:4201/*`
   - Web Origins: `http://localhost:4201`

## **Testing Steps:**

1. **Test Keycloak Server:**
   ```
   http://localhost:8081
   ```

2. **Test Realm Endpoint:**
   ```
   http://localhost:8081/realms/BNG_Elearning_realm
   ```

3. **Test Token Endpoint:**
   ```
   http://localhost:8081/realms/BNG_Elearning_realm/protocol/openid-connect/token
   ```

4. **Test with Proxy:**
   ```
   http://localhost:4201/api/realms/BNG_Elearning_realm/protocol/openid-connect/token
   ```

## **Next Steps:**
1. Verify Keycloak server is running
2. Check realm and client configuration
3. Test endpoints manually
4. Update configuration based on findings

## **Contact Information:**
If issues persist, provide:
- Keycloak version
- Exact error messages from browser console
- Keycloak server logs
- Network tab details from browser dev tools
