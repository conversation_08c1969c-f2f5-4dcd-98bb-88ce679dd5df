import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { FormGroup } from "@angular/forms";

@Component({
  selector: "app-register",
  templateUrl: "register.component.html",
  styleUrls: ["./register.component.scss"]
})
export class RegisterComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef;

  registerForm: FormGroup;
  focus: boolean = false;
  focus1: boolean = false;
  focus2: boolean = false;
  focus3: boolean = false;
  focus4: boolean = false;
  focus5: boolean = false;
  selectedRole: string = '';
  imagePreview: string | ArrayBuffer | null = null;
  selectedFile: File | null = null;
  isImageLoading: boolean = false;

  constructor() {}

  ngOnInit() {}

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  onImageUpload(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Veuillez sélectionner un fichier image valide.');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('La taille du fichier ne doit pas dépasser 5MB.');
        return;
      }

      this.isImageLoading = true;
      this.selectedFile = file;
      const reader = new FileReader();

      reader.onload = () => {
        this.imagePreview = reader.result;
        this.isImageLoading = false;

        // Add fade-in animation class
        setTimeout(() => {
          const imgElement = document.querySelector('.profile-image') as HTMLElement;
          if (imgElement) {
            imgElement.classList.add('fade-in');
            setTimeout(() => imgElement.classList.remove('fade-in'), 500);
          }
        }, 10);
      };

      reader.onerror = () => {
        this.isImageLoading = false;
        alert('Erreur lors du chargement de l\'image. Veuillez réessayer.');
      };

      reader.readAsDataURL(file);

      if (this.registerForm) {
        this.registerForm.patchValue({ image: file });
      }
    }
  }
  
}
