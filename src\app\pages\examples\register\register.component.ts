import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { FormGroup, FormBuilder, Validators, AbstractControl } from "@angular/forms";
import { Router } from "@angular/router";
import { AuthService } from "../../../core/services/auth-service.service";

@Component({
  selector: "app-register",
  templateUrl: "register.component.html",
  styleUrls: ["./register.component.scss"]
})
export class RegisterComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef;

  registerForm: FormGroup;
  focus: boolean = false;
  focus1: boolean = false;
  focus2: boolean = false;
  focus3: boolean = false;
  focus4: boolean = false;
  focus5: boolean = false;
  imagePreview: string | ArrayBuffer | null = null;
  selectedFile: File | null = null;
  isImageLoading: boolean = false;
  isLoading: boolean = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.initializeForm();
  }

  ngOnInit() {}

  private initializeForm(): void {
    this.registerForm = this.fb.group({
      prenom: ['', [Validators.required, Validators.minLength(2)]],
      nom: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      role: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  private passwordMatchValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (!password || !confirmPassword) {
      return null;
    }

    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.registerForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getErrorMessage(fieldName: string): string {
    const field = this.registerForm.get(fieldName);
    if (!field || !field.errors) return '';

    if (field.errors['required']) {
      switch (fieldName) {
        case 'prenom': return 'First name is required';
        case 'nom': return 'Last name is required';
        case 'email': return 'L\'email is required';
        case 'password': return 'Password is required';
        case 'confirmPassword': return 'Confirm password is required';
        case 'role': return 'Role is required';
        default: return 'This field is required';
      }
    }

    if (field.errors['email']) {
      return 'Please enter a valid name';
    }

    if (field.errors['minlength']) {
      const requiredLength = field.errors['minlength'].requiredLength;
      switch (fieldName) {
        case 'password': return `Password should contain at least ${requiredLength} characters`;
        case 'prenom':
        case 'nom': return `This field should contain at least ${requiredLength} characters`;
        default: return `Minimum ${requiredLength} characters required  `;
      }
    }

    if (this.registerForm.errors?.['passwordMismatch'] && fieldName === 'confirmPassword') {
      return 'These passwords do not correspond to each others';
    }

    return '';
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  onSubmit(): void {
    if (this.registerForm.valid) {
      this.isLoading = true;
      this.errorMessage = null;
      this.successMessage = null;

      const formData = this.registerForm.value;

      // Call the auth service register method
      this.authService.register(
        formData.nom,
        formData.prenom,
        formData.email,
        formData.password,
        formData.role,
        this.selectedFile,
        '', // age - not collected in this form
        '', // gender - not collected in this form
        ''  // country - not collected in this form
      ).subscribe({
        next: (response) => {
          console.log('Registration successful:', response);
          this.isLoading = false;
          this.successMessage = 'Account succefully created! You can now login.';

          // Reset form after successful registration
          setTimeout(() => {
            this.router.navigate(['/examples/login']);
          }, 2000);
        },
        error: (error) => {
          console.error('Registration failed:', error);
          this.isLoading = false;

          if (error.status === 400) {
            this.errorMessage = 'Invalid data. Please check your information';
          } else if (error.status === 409) {
            this.errorMessage = 'An account with this mail already exists';
          } else if (error.status === 0) {
            this.errorMessage = 'Server Error. Please verify your internet connection';
          } else {
            this.errorMessage = error.error?.message || 'An error while creating your account. Please try again later.';
          }
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.registerForm.controls).forEach(key => {
        this.registerForm.get(key)?.markAsTouched();
      });
      this.errorMessage = 'Please correct the errors in the form';
    }
  }

  onImageUpload(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Image size should not exceed 5MB.');
        return;
      }

      this.isImageLoading = true;
      this.selectedFile = file;
      const reader = new FileReader();

      reader.onload = () => {
        this.imagePreview = reader.result;
        this.isImageLoading = false;

        // Add fade-in animation class
        setTimeout(() => {
          const imgElement = document.querySelector('.profile-image') as HTMLElement;
          if (imgElement) {
            imgElement.classList.add('fade-in');
            setTimeout(() => imgElement.classList.remove('fade-in'), 500);
          }
        }, 10);
      };

      reader.onerror = () => {
        this.isImageLoading = false;
        alert('Error while uploading your image. Please try again later.');
      };

      reader.readAsDataURL(file);

      if (this.registerForm) {
        this.registerForm.patchValue({ image: file });
      }
    }
  }
  
}
