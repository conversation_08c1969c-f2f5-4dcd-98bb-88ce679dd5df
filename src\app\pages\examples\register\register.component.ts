import { Component, OnInit } from "@angular/core";
import { FormGroup } from "@angular/forms";

@Component({
  selector: "app-register",
  templateUrl: "register.component.html"
})
export class RegisterComponent implements OnInit {
  registerForm: FormGroup;
  focus;
  focus1;
  focus2;
  focus3;
  focus4;
  focus5;
  selectedRole: string = '';
  imagePreview: string | ArrayBuffer | null = null;
  constructor() {}

  ngOnInit() {}

   onImageUpload(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreview = reader.result;
      };
      reader.readAsDataURL(file);
      this.registerForm.patchValue({ image: file }); // Adding the file to the form control
    }
  }
}
