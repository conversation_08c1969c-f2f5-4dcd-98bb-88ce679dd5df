// Login page specific styles with exact golden colors

.custom-golden-header {
  background: #FFCC00 !important;
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
  position: relative;
  overflow: hidden;
  
  // Remove any existing background images or patterns
  &::before,
  &::after {
    display: none !important;
  }
  
  // Ensure all text is dark for contrast
  h1, h2, h3, h4, h5, h6 {
    color: #1C1F21 !important;
    text-shadow: none !important;
    font-weight: 700 !important;
  }
  
  p, span, small {
    color: #1C1F21 !important;
    text-shadow: none !important;
  }
  
  // Override any inherited styles
  * {
    color: #1C1F21 !important;
  }
}

// Separator styling to match the golden theme
.separator {
  .fill-default {
    fill: #FFFBEB !important;
  }
}

// Card styling improvements
.card {
  background-color: #FFFBEB !important;
  border: 1px solid rgba(255, 204, 0, 0.2) !important;
  box-shadow: 0 15px 35px rgba(28, 31, 33, 0.1) !important;
  
  .card-header {
    background-color: transparent !important;
    border-bottom: 1px solid rgba(255, 204, 0, 0.1) !important;
  }
}

// Button styling for social login
.btn-neutral {
  background-color: #FFFBEB !important;
  border: 2px solid #FFCC00 !important;
  color: #1C1F21 !important;
  
  &:hover {
    background-color: #FFEFAD !important;
    border-color: #FFDA47 !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 204, 0, 0.3);
  }
}

// Form styling
.form-control {
  background-color: #FFFBEB !important;
  border: 2px solid rgba(255, 204, 0, 0.3) !important;
  color: #1C1F21 !important;
  
  &:focus {
    background-color: #fff !important;
    border-color: #FFCC00 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 204, 0, 0.25) !important;
    color: #1C1F21 !important;
  }
  
  &::placeholder {
    color: rgba(28, 31, 33, 0.6) !important;
  }
}

.input-group-text {
  background-color: #FFEFAD !important;
  border: 2px solid rgba(255, 204, 0, 0.3) !important;
  color: #1C1F21 !important;
}

// reCAPTCHA container styling
.form-group {
  .d-flex.justify-content-center {
    background-color: rgba(255, 251, 235, 0.5);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(255, 204, 0, 0.2);
  }
}

// Footer links styling
.text-primary {
  color: #FFCC00 !important;
  
  &:hover {
    color: #FFDA47 !important;
    text-decoration: none;
  }
}

// Error message styling
.text-danger {
  color: #dc3545 !important;
  background-color: rgba(220, 53, 69, 0.1);
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #dc3545;
}

// Loading spinner color
.spinner-border {
  color: #1C1F21 !important;
}

// Responsive adjustments
@media (max-width: 768px) {
  .custom-golden-header {
    padding: 3rem 0 !important;
    
    h1 {
      font-size: 2rem !important;
    }
    
    p {
      font-size: 1rem !important;
    }
  }
  
  .card {
    margin: 0 15px;
  }
}
