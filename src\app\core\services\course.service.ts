import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Course } from '../models/Course';

@Injectable({
  providedIn: 'root'
})
export class CourseService {
  private apiUrl = 'http://localhost:8080/cours';

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  private getAuthHeadersForFormData(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for FormData, let browser set it
    });
  }

  // Get all courses
  getAllCourses(): Observable<Course[]> {
    return this.http.get<Course[]>(`${this.apiUrl}/retrieveAllCours`, {
      headers: this.getAuthHeaders()
    });
  }

  // Get course by ID
  getCourseById(id: number): Observable<Course> {
    return this.http.get<Course>(`${this.apiUrl}/getCourseById/${id}`, {
      headers: this.getAuthHeaders()
    });
  }
addCours(cours: Course, imageFile: File, fichierFile?: File, videoFile?: File): Observable<Course> {
    const formData = new FormData();
    formData.append('cours', new Blob([JSON.stringify(cours)], {
      type: 'application/json'
    }));
    formData.append('image', imageFile);
    if (fichierFile) formData.append('fichier', fichierFile);
    if (videoFile) formData.append('video', videoFile);

    return this.http.post<Course>(`${this.apiUrl}/addCours`, formData, {
      headers: this.getAuthHeadersForFormData()
    });
  }

  // Add new course (wrapper method for component compatibility)
  addCourse(
    titre: string,
    description: string,
    duree: string,
    niveau: string,
    categorieId: number,
    prix: number,
    nbMaxEtudiant: number,
    format: string,
    image: File | null,
    video: File | null,
    fichier: File | null
  ): Observable<any> {
    const cours = {
      titre: titre,
      description: description,
      duree: duree,
      niveau: niveau,
      prix: prix,
      nb_max_etudiant: nbMaxEtudiant || 50,
      format: format,
      date_publication: new Date().toISOString().slice(0, 10), // optional
      categorie: {
        id: categorieId // Use the category ID from the form
      }
    };

    // Image is required, so we need to handle the case where it's null
    if (!image) {
      throw new Error('Image is required for course creation');
    }

    const formData = new FormData();

    // Add the course object as JSON blob
    formData.append('cours', new Blob([JSON.stringify(cours)], {
      type: 'application/json'
    }));

    // Add files
    formData.append('image', image);
    if (fichier) formData.append('fichier', fichier);
    if (video) formData.append('video', video);

    return this.http.post<Course>(`${this.apiUrl}/addCours`, formData, {
      headers: this.getAuthHeadersForFormData()
    });
  }



  // Update course (direct method - matches Spring Boot API)
  updateCours(cours: Course, imageFile?: File, fichierFile?: File, videoFile?: File): Observable<Course> {
    const formData = new FormData();

    // Add the course object as JSON blob (same structure as addCours)
    formData.append('cours', new Blob([JSON.stringify(cours)], {
      type: 'application/json'
    }));

    // Add files only if provided (optional updates - pictures not necessarily updated)
    if (imageFile) {
      formData.append('image', imageFile, imageFile.name);
    }
    if (fichierFile) {
      formData.append('fichier', fichierFile, fichierFile.name);
    }
    if (videoFile) {
      formData.append('video', videoFile, videoFile.name);
    }

    return this.http.put<Course>(`${this.apiUrl}/updateCours`, formData, {
      headers: this.getAuthHeadersForFormData()
    });
  }

  // Update course (wrapper method for component compatibility)
  updateCourse(
    courseId: number,
    titre: string,
    description: string,
    duree: string,
    niveau: string,
    categorieId: number,
    prix: number,
    nbMaxEtudiant: number,
    format: string,
    image: File | null,
    video: File | null,
    fichier: File | null
  ): Observable<any> {
    const cours = {
      id_cours: courseId,
      titre: titre,
      description: description,
      duree: duree,
      niveau: niveau,
      prix: prix,
      nb_max_etudiant: nbMaxEtudiant || 50,
      format: format,
      date_publication: new Date().toISOString().slice(0, 10),
      categorie: {
        id: categorieId
      }
    };

    const formData = new FormData();

    // Add the course object as JSON blob
    formData.append('cours', new Blob([JSON.stringify(cours)], {
      type: 'application/json'
    }));

    // Add files only if provided (optional updates)
    if (image) formData.append('image', image);
    if (fichier) formData.append('fichier', fichier);
    if (video) formData.append('video', video);

    return this.http.put<Course>(`${this.apiUrl}/updateCours`, formData, {
      headers: this.getAuthHeadersForFormData()
    });
  }

  // Delete course
  deleteCourse(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/deleteCours/${id}`, {
      headers: this.getAuthHeaders()
    });
  }

  // Get courses by category
  getCoursesByCategory(category: string): Observable<Course[]> {
    return this.http.get<Course[]>(`${this.apiUrl}/getCoursesByCategory/${category}`, {
      headers: this.getAuthHeaders()
    });
  }

  // Get courses by instructor
  getCoursesByInstructor(instructor: string): Observable<Course[]> {
    return this.http.get<Course[]>(`${this.apiUrl}/getCoursesByInstructor/${instructor}`, {
      headers: this.getAuthHeaders()
    });
  }
}
