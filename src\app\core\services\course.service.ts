import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Course } from '../models/Course';

@Injectable({
  providedIn: 'root'
})
export class CourseService {
  private apiUrl = 'http://localhost:8080/cours';

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  private getAuthHeadersForFormData(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for FormData, let browser set it
    });
  }

  // Get all courses
  getAllCourses(): Observable<Course[]> {
    return this.http.get<Course[]>(`${this.apiUrl}/retrieveAllCours`, {
      headers: this.getAuthHeaders()
    });
  }

  // Get course by ID
  getCourseById(id: number): Observable<Course> {
    return this.http.get<Course>(`${this.apiUrl}/getCourseById/${id}`, {
      headers: this.getAuthHeaders()
    });
  }

  // Add new course
  addCourse(
    title: string,
    description: string,
    instructor: string,
    duration: string,
    level: string,
    category: string,
    price: number,
    maxStudents: number,
    status: string,
    image: File | null
  ): Observable<any> {
    const formData = new FormData();
    formData.append('title', title);
    formData.append('description', description);
    formData.append('instructor', instructor);
    formData.append('duration', duration);
    formData.append('level', level);
    formData.append('category', category);
    formData.append('price', price.toString());
    formData.append('maxStudents', maxStudents.toString());
    formData.append('status', status);

    if (image) {
      formData.append('image', image, image.name);
    }

    return this.http.post(`${this.apiUrl}/addCourse`, formData, {
      headers: this.getAuthHeadersForFormData()
    });
  }

  // Update course
  updateCourse(
    courseId: number,
    title: string,
    description: string,
    instructor: string,
    duration: string,
    level: string,
    category: string,
    price: number,
    maxStudents: number,
    status: string,
    image: File | null
  ): Observable<any> {
    const formData = new FormData();
    formData.append('courseId', courseId.toString());
    formData.append('title', title);
    formData.append('description', description);
    formData.append('instructor', instructor);
    formData.append('duration', duration);
    formData.append('level', level);
    formData.append('category', category);
    formData.append('price', price.toString());
    formData.append('maxStudents', maxStudents.toString());
    formData.append('status', status);

    if (image) {
      formData.append('image', image, image.name);
    }

    return this.http.put(`${this.apiUrl}/updateCourse`, formData, {
      headers: this.getAuthHeadersForFormData()
    });
  }

  // Delete course
  deleteCourse(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/deleteCourse/${id}`, {
      headers: this.getAuthHeaders()
    });
  }

  // Get courses by category
  getCoursesByCategory(category: string): Observable<Course[]> {
    return this.http.get<Course[]>(`${this.apiUrl}/getCoursesByCategory/${category}`, {
      headers: this.getAuthHeaders()
    });
  }

  // Get courses by instructor
  getCoursesByInstructor(instructor: string): Observable<Course[]> {
    return this.http.get<Course[]>(`${this.apiUrl}/getCoursesByInstructor/${instructor}`, {
      headers: this.getAuthHeaders()
    });
  }
}
