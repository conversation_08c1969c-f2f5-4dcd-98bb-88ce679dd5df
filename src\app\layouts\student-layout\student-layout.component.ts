import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-student-layout',
  templateUrl: './student-layout.component.html',
  styleUrls: ['./student-layout.component.scss']
})
export class StudentLayoutComponent implements OnInit {
  studentName: string = '';
  currentYear: number = new Date().getFullYear();

  constructor(private router: Router) { }

  ngOnInit(): void {
    // Get student name from localStorage or API
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const decodedToken = this.decodeToken(token);
        if (decodedToken) {
          this.studentName = decodedToken.email || decodedToken.name || 'Student';
        }
      } catch (error) {
        console.error('Error decoding token:', error);
        this.studentName = 'Student';
      }
    }
  }

  decodeToken(token: string): any {
    try {
      const payload = token.split('.')[1];
      return JSON.parse(atob(payload));
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  logout(): void {
    localStorage.removeItem('token');
    this.router.navigate(['/examples/login']);
  }
}
