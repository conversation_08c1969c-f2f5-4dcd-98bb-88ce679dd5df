import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';

import { StudentLayoutComponent } from './student-layout.component';

@NgModule({
  declarations: [
    // StudentLayoutComponent is already declared in app.module.ts
  ],
  imports: [
    CommonModule,
    RouterModule,
    BsDropdownModule.forRoot()
  ],
  exports: [
    StudentLayoutComponent
  ]
})
export class StudentLayoutModule { }
