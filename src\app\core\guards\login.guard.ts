import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { TokenService } from '../services/token-service.service';

@Injectable({
  providedIn: 'root'
})
export class LoginGuard implements CanActivate {

  constructor(
    private tokenService: TokenService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    const token = this.tokenService.getAccessToken();
    
    if (token && !this.tokenService.isTokenExpired()) {
      // If user is already authenticated, redirect to dashboard
      this.router.navigate(['/dashboards/dashboard']);
      return false;
    }
    
    // If not authenticated, allow access to login page
    return true;
  }
}
