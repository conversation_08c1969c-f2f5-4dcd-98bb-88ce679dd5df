.profile-image-container {
  position: relative;
  display: inline-block;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.profile-image-container:hover {
  border-color: #5e72e4;
  transform: scale(1.05);
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.profile-image-container:hover .profile-image {
  filter: brightness(0.7);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  border-radius: 50%;
}

.profile-image-container:hover .image-overlay {
  opacity: 1;
}

.image-overlay i {
  font-size: 24px;
  color: white;
}

.cursor-pointer {
  cursor: pointer;
}

/* Camera icon badge */
.profile-image-container::after {
  content: '\f030'; /* FontAwesome camera icon */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 28px;
  height: 28px;
  background-color: #5e72e4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  opacity: 0.8;
}

.profile-image-container:hover::after {
  background-color: #4c63d2;
  transform: scale(1.1);
  opacity: 1;
}

/* Responsive design */
@media (max-width: 768px) {
  .profile-image-container {
    width: 100px;
    height: 100px;
  }
  
  .image-overlay i {
    font-size: 20px;
  }
}

/* Loading state */
.profile-image-container.loading {
  pointer-events: none;
}

.profile-image-container.loading .image-overlay {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.7);
}

/* Animation for image change */
.profile-image.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse animation for loading */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.profile-image-container.loading {
  animation: pulse 1.5s ease-in-out infinite;
}
