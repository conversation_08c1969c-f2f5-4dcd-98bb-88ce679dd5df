// Tables page styling with golden theme

.custom-golden-header {
  background: #FFCC00 !important;
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
  position: relative;
  overflow: hidden;
  
  // Remove any existing background images or patterns
  &::before,
  &::after {
    display: none !important;
  }
  
  // Ensure all text is dark for contrast
  h1, h2, h3, h4, h5, h6 {
    color: #1C1F21 !important;
    text-shadow: none !important;
    font-weight: 700 !important;
  }
  
  p, span, small, a {
    color: #1C1F21 !important;
    text-shadow: none !important;
  }
  
  // Breadcrumb styling
  .breadcrumb {
    background-color: transparent !important;
    
    .breadcrumb-item {
      a {
        color: #1C1F21 !important;
        
        &:hover {
          color: rgba(28, 31, 33, 0.8) !important;
        }
      }
      
      &.active {
        color: rgba(28, 31, 33, 0.7) !important;
      }
    }
  }
  
  // Button styling in header
  .btn-neutral {
    background-color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid #1C1F21 !important;
    color: #1C1F21 !important;
    
    &:hover {
      background-color: #fff !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(28, 31, 33, 0.15);
    }
  }
}

// Card styling
.card {
  background-color: #FFFBEB !important;
  border: 1px solid rgba(255, 204, 0, 0.2) !important;
  box-shadow: 0 8px 25px rgba(28, 31, 33, 0.08) !important;
  
  .card-header {
    background-color: rgba(255, 239, 173, 0.3) !important;
    border-bottom: 1px solid rgba(255, 204, 0, 0.2) !important;
    
    h3 {
      color: #1C1F21 !important;
    }
  }
}

// Table styling
.table {
  background-color: transparent !important;
  
  thead {
    &.thead-light {
      background-color: #FFEFAD !important;
      
      th {
        color: #1C1F21 !important;
        border-color: rgba(255, 204, 0, 0.2) !important;
        font-weight: 600;
      }
    }
  }
  
  tbody {
    tr {
      border-color: rgba(255, 204, 0, 0.1) !important;
      
      &:hover {
        background-color: rgba(255, 251, 235, 0.8) !important;
      }
      
      td {
        color: #1C1F21 !important;
        border-color: rgba(255, 204, 0, 0.1) !important;
        
        .text-muted {
          color: rgba(28, 31, 33, 0.7) !important;
        }
        
        .text-primary {
          color: #FFCC00 !important;
        }
      }
    }
  }
  
  // Striped table styling
  &.table-striped {
    tbody tr:nth-of-type(odd) {
      background-color: rgba(255, 239, 173, 0.1) !important;
    }
  }
}

// Table actions styling
.table-actions {
  .table-action {
    color: #FFCC00 !important;
    margin: 0 5px;
    transition: all 0.3s ease;
    
    &:hover {
      color: #FFDA47 !important;
      transform: scale(1.1);
    }
    
    &.table-action-delete {
      color: #f5365c !important;
      
      &:hover {
        color: #ec0c38 !important;
      }
    }
  }
}

// Avatar styling
.avatar {
  border: 2px solid rgba(255, 204, 0, 0.3);
}

// Export button styling
.btn-primary {
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
  border-color: #FFCC00 !important;
  color: #1C1F21 !important;
  
  &:hover {
    background: linear-gradient(135deg, #FFDA47 0%, #FFCC00 100%) !important;
    border-color: #FFDA47 !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 204, 0, 0.4);
  }
}

// Container background
.container-fluid {
  background-color: transparent !important;
}

// Main content background
body {
  background-color: #FFFBEB !important;
}
