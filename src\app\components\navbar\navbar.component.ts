import { Component, OnInit, ElementRef } from "@angular/core";
import { ROUTES } from "../sidebar/sidebar.component";
import { Router, Event, NavigationStart, NavigationEnd, NavigationError } from '@angular/router';

import {
  Location,
  LocationStrategy,
  PathLocationStrategy
} from "@angular/common";
import { AuthService } from "src/app/core/services/auth-service.service";

@Component({
  selector: "app-navbar",
  templateUrl: "./navbar.component.html",
  styleUrls: ["./navbar.component.scss"]
})
export class NavbarComponent implements OnInit {
  userEmail: string | null = null;
  public focus;
  public listTitles: any[];
  public location: Location;
  sidenavOpen: boolean = true;
  constructor(
    location: Location,
    private element: ElementRef,
    private router: Router, 
    private authService: AuthService
  ) {
    this.location = location;
    this.router.events.subscribe((event: Event) => {
       if (event instanceof NavigationStart) {
           // Show loading indicator

       }
       if (event instanceof NavigationEnd) {
           // Hide loading indicator

           if (window.innerWidth < 1200) {
             document.body.classList.remove("g-sidenav-pinned");
             document.body.classList.add("g-sidenav-hidden");
             this.sidenavOpen = false;
           }
       }

       if (event instanceof NavigationError) {
           // Hide loading indicator

           // Present error to user
           console.log(event.error);
       }
   });

  }

  ngOnInit() {
    this.listTitles = ROUTES.filter(listTitle => listTitle);

    const token = localStorage.getItem('token');

    if (token) {
      // Decode the token and extract the email
      const decodedToken = this.decodeToken(token);

      if (decodedToken) {
        this.userEmail = decodedToken.email;// Extract the email from the decoded token
        console.log('User email:', this.userEmail); // You can use the email here
      } else {
        console.error('Failed to decode token');
      }
    } else {
      console.error('No token found in localStorage');
    }
  }

  decodeToken(token: string): any {
    try {
      const payload = token.split('.')[1]; // Get the payload part (second part of the JWT)
      return JSON.parse(atob(payload)); // Decode and parse the payload
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }
  getTitle() {
    var titlee = this.location.prepareExternalUrl(this.location.path());
    if (titlee.charAt(0) === "#") {
      titlee = titlee.slice(1);
    }

    for (var item = 0; item < this.listTitles.length; item++) {
      if (this.listTitles[item].path === titlee) {
        return this.listTitles[item].title;
      }
    }
    return "Dashboard";
  }

  openSearch() {
    document.body.classList.add("g-navbar-search-showing");
    setTimeout(function() {
      document.body.classList.remove("g-navbar-search-showing");
      document.body.classList.add("g-navbar-search-show");
    }, 150);
    setTimeout(function() {
      document.body.classList.add("g-navbar-search-shown");
    }, 300);
  }
  closeSearch() {
    document.body.classList.remove("g-navbar-search-shown");
    setTimeout(function() {
      document.body.classList.remove("g-navbar-search-show");
      document.body.classList.add("g-navbar-search-hiding");
    }, 150);
    setTimeout(function() {
      document.body.classList.remove("g-navbar-search-hiding");
      document.body.classList.add("g-navbar-search-hidden");
    }, 300);
    setTimeout(function() {
      document.body.classList.remove("g-navbar-search-hidden");
    }, 500);
  }
  openSidebar() {
    if (document.body.classList.contains("g-sidenav-pinned")) {
      document.body.classList.remove("g-sidenav-pinned");
      document.body.classList.add("g-sidenav-hidden");
      this.sidenavOpen = false;
    } else {
      document.body.classList.add("g-sidenav-pinned");
      document.body.classList.remove("g-sidenav-hidden");
      this.sidenavOpen = true;
    }
  }
  toggleSidenav() {
    const sidenav = document.getElementById("sidenav-main");

    if (document.body.classList.contains("g-sidenav-pinned")) {
      // Hide sidebar completely
      document.body.classList.remove("g-sidenav-pinned");
      document.body.classList.add("g-sidenav-hidden");
      if (sidenav) {
        sidenav.style.transform = "translateX(-100%)";
        sidenav.style.transition = "transform 0.3s ease";
      }
      this.sidenavOpen = false;
    } else {
      // Show sidebar
      document.body.classList.add("g-sidenav-pinned");
      document.body.classList.remove("g-sidenav-hidden");
      if (sidenav) {
        sidenav.style.transform = "translateX(0)";
        sidenav.style.transition = "transform 0.3s ease";
      }
      this.sidenavOpen = true;
    }
  }
  logout() {
    this.authService.logout();
    this.router.navigate(['/examples/login']);
  }
}
