/* You can add global styles to this file, and also import other style files */

/* Golden Theme Global Overrides */

// Primary button styling with golden theme
.btn-primary {
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
  border-color: #FFCC00 !important;
  color: #1C1F21 !important;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(255, 204, 0, 0.3);
  transition: all 0.3s ease;

  &:hover,
  &:focus,
  &:active {
    background: linear-gradient(135deg, #FFDA47 0%, #FFCC00 100%) !important;
    border-color: #FFDA47 !important;
    color: #000 !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 204, 0, 0.4);
  }

  &:disabled {
    background: #FFEFAD !important;
    border-color: #FFEFAD !important;
    color: rgba(28, 31, 33, 0.6) !important;
    transform: none;
    box-shadow: none;
  }
}

// Secondary button styling
.btn-secondary {
  background-color: #FFFBEB !important;
  border-color: #FFCC00 !important;
  color: #1C1F21 !important;

  &:hover,
  &:focus,
  &:active {
    background-color: #FFEFAD !important;
    border-color: #FFDA47 !important;
    color: #1C1F21 !important;
  }
}

// Card styling
.card {
  border: 1px solid rgba(255, 204, 0, 0.2);
  box-shadow: 0 8px 30px rgba(28, 31, 33, 0.1);

  &.bg-secondary {
    background-color: #FFFBEB !important;
  }
}

// Form controls
.form-control {
  border-color: rgba(255, 204, 0, 0.3);

  &:focus {
    border-color: #FFCC00;
    box-shadow: 0 0 0 0.2rem rgba(255, 204, 0, 0.25);
  }
}

// Input group styling
.input-group-text {
  background-color: #FFEFAD;
  border-color: rgba(255, 204, 0, 0.3);
  color: #1C1F21;
}

// Alert styling
.alert-success {
  background-color: rgba(255, 239, 173, 0.8);
  border-color: #FFCC00;
  color: #1C1F21;
}

.alert-danger {
  background-color: rgba(245, 54, 92, 0.1);
  border-color: #f5365c;
  color: #1C1F21;
}

.alert-info {
  background-color: rgba(255, 218, 71, 0.2);
  border-color: #FFDA47;
  color: #1C1F21;
}

// Text colors
.text-primary {
  color: #FFCC00 !important;
}

.text-dark {
  color: #1C1F21 !important;
}

// Background gradients
.bg-gradient-primary {
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 50%, #FFEFAD 100%) !important;
}

// Custom header with your exact colors
.custom-golden-header {
  background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
  position: relative;

  // Ensure proper text contrast
  h1, h2, h3, h4, h5, h6, p, span {
    color: #1C1F21 !important;
    text-shadow: none !important;
  }

  // Override any existing gradients or backgrounds
  &::before {
    display: none !important;
  }
}

// Link styling
a {
  color: #FFCC00;

  &:hover {
    color: #FFDA47;
    text-decoration: none;
  }
}

// Custom golden theme utilities
.bg-golden-light {
  background-color: #FFFBEB !important;
}

.bg-golden-medium {
  background-color: #FFEFAD !important;
}

.bg-golden-primary {
  background-color: #FFCC00 !important;
}

.text-golden {
  color: #FFCC00 !important;
}

.border-golden {
  border-color: #FFCC00 !important;
}
