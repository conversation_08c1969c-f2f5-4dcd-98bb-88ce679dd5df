import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth-service.service';

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    
    const expectedRoles = route.data['expectedRoles'] as Array<string>;
    
    if (!expectedRoles) {
      return true; // No role restriction
    }

    // Get user role from token or localStorage
    const userRole = this.getUserRole();
    
    if (!userRole) {
      // No role found, redirect to login
      this.router.navigate(['/examples/login']);
      return false;
    }

    // Check if user has any of the expected roles
    const hasRole = expectedRoles.includes(userRole);
    
    if (!hasRole) {
      // User doesn't have required role
      if (userRole === 'STUDENT') {
        // Redirect students to courses page
        this.router.navigate(['/courses']);
      } else {
        // Redirect others to dashboard
        this.router.navigate(['/dashboards/dashboard']);
      }
      return false;
    }

    return true;
  }

  private getUserRole(): string | null {
    try {
      const token = localStorage.getItem('token') || localStorage.getItem('access_token');
      if (!token) {
        return null;
      }

      // Try to decode the token to get role
      const payload = this.decodeToken(token);
      if (payload) {
        // Check for role in different possible locations
        const roles = payload?.resource_access?.['BNG-Elearning-rest-api']?.roles || [];
        if (roles.length > 0) {
          return roles[0]; // Return first role
        }
        
        // Check for direct role property
        if (payload.role) {
          return payload.role;
        }
      }

      // Fallback: check if user data is stored separately
      const userData = localStorage.getItem('userData');
      if (userData) {
        const user = JSON.parse(userData);
        return user.role;
      }

      return null;
    } catch (error) {
      console.error('Error getting user role:', error);
      return null;
    }
  }

  private decodeToken(token: string): any {
    try {
      const payload = token.split('.')[1];
      const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(window.atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }
}
