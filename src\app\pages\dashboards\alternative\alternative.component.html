<div class=" header pb-6">
  <div class=" container-fluid">
    <div class=" header-body">
      <div class=" row align-items-center py-4">
        <div class=" col-lg-6 col-7">
          <h6 class=" h2 d-inline-block mb-0">Alternative</h6>

          <nav
            aria-label="breadcrumb"
            class=" d-none d-md-inline-block ml-md-4"
          >
            <ol class=" breadcrumb breadcrumb-links">
              <li class=" breadcrumb-item">
                <a href="javascript:void(0)"> <i class=" fas fa-home"> </i> </a>
              </li>

              <li class=" breadcrumb-item">
                <a href="javascript:void(0)"> Dashboards </a>
              </li>

              <li aria-current="page" class=" breadcrumb-item active">
                Alternative
              </li>
            </ol>
          </nav>
        </div>

        <div class=" col-lg-6 col-5 text-right">
          <a class=" btn btn-sm btn-neutral" href="javascript:void(0)"> New </a>

          <a class=" btn btn-sm btn-neutral" href="javascript:void(0)">
            Filters
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class=" container-fluid mt--6">
  <div class=" row">
    <div class=" col-xl-3 col-md-6">
      <div class=" card bg-gradient-primary border-0">
        <div class=" card-body">
          <div class=" row">
            <div class=" col">
              <h5 class=" card-title text-uppercase text-muted mb-0 text-white">
                Tasks completed
              </h5>

              <span class=" h2 font-weight-bold mb-0 text-white"> 8/24 </span>

              <div class=" progress progress-xs mt-3 mb-0">
                <progressbar type="success" [value]="30"> </progressbar>
              </div>
            </div>

            <div class=" col-auto">
              <div dropdown class="dropdown no-caret">
                <button
                  id="button-basic"
                  dropdownToggle
                  type="button"
                  class="btn btn-sm btn-neutral mr-0 dropdown-toggle"
                  aria-controls="dropdown-basic"
                >
                  <i class=" fas fa-ellipsis-h"> </i>
                </button>

                <div
                  class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                  *dropdownMenu
                >
                  <a class="dropdown-item" href="javascript:void(0)">
                    Action
                  </a>

                  <a class="dropdown-item" href="javascript:void(0)">
                    Another action
                  </a>

                  <a class="dropdown-item" href="javascript:void(0)">
                    Something else here
                  </a>
                </div>
              </div>
            </div>
          </div>

          <p class=" mt-3 mb-0 text-sm">
            <a
              class=" text-nowrap text-white font-weight-600"
              href="javascript:void(0)"
            >
              See details
            </a>
          </p>
        </div>
      </div>
    </div>

    <div class=" col-xl-3 col-md-6">
      <div class=" card bg-gradient-info border-0">
        <div class=" card-body">
          <div class=" row">
            <div class=" col">
              <h5 class=" card-title text-uppercase text-muted mb-0 text-white">
                Contacts
              </h5>

              <span class=" h2 font-weight-bold mb-0 text-white">
                123/267
              </span>

              <div class=" progress progress-xs mt-3 mb-0">
                <progressbar type="success" [value]="50"> </progressbar>
              </div>
            </div>

            <div class=" col-auto">
              <div dropdown class="dropdown no-caret">
                <button
                  id="button-basic"
                  dropdownToggle
                  type="button"
                  class="btn btn-sm btn-neutral mr-0 dropdown-toggle"
                  aria-controls="dropdown-basic"
                >
                  <i class=" fas fa-ellipsis-h"> </i>
                </button>

                <div
                  class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                  *dropdownMenu
                >
                  <a class="dropdown-item" href="javascript:void(0)">
                    Action
                  </a>

                  <a class="dropdown-item" href="javascript:void(0)">
                    Another action
                  </a>

                  <a class="dropdown-item" href="javascript:void(0)">
                    Something else here
                  </a>
                </div>
              </div>
            </div>
          </div>

          <p class=" mt-3 mb-0 text-sm">
            <a
              class=" text-nowrap text-white font-weight-600"
              href="javascript:void(0)"
            >
              See details
            </a>
          </p>
        </div>
      </div>
    </div>

    <div class=" col-xl-3 col-md-6">
      <div class=" card bg-gradient-danger border-0">
        <div class=" card-body">
          <div class=" row">
            <div class=" col">
              <h5 class=" card-title text-uppercase text-muted mb-0 text-white">
                Items sold
              </h5>

              <span class=" h2 font-weight-bold mb-0 text-white">
                200/300
              </span>

              <div class=" progress progress-xs mt-3 mb-0">
                <progressbar type="success" [value]="80"> </progressbar>
              </div>
            </div>

            <div class=" col-auto">
              <div dropdown class="dropdown no-caret">
                <button
                  id="button-basic"
                  dropdownToggle
                  type="button"
                  class="btn btn-sm btn-neutral mr-0 dropdown-toggle"
                  aria-controls="dropdown-basic"
                >
                  <i class=" fas fa-ellipsis-h"> </i>
                </button>

                <div
                  class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                  *dropdownMenu
                >
                  <a class="dropdown-item" href="javascript:void(0)">
                    Action
                  </a>

                  <a class="dropdown-item" href="javascript:void(0)">
                    Another action
                  </a>

                  <a class="dropdown-item" href="javascript:void(0)">
                    Something else here
                  </a>
                </div>
              </div>
            </div>
          </div>

          <p class=" mt-3 mb-0 text-sm">
            <a
              class=" text-nowrap text-white font-weight-600"
              href="javascript:void(0)"
            >
              See details
            </a>
          </p>
        </div>
      </div>
    </div>

    <div class=" col-xl-3 col-md-6">
      <div class=" card bg-gradient-default border-0">
        <div class=" card-body">
          <div class=" row">
            <div class=" col">
              <h5 class=" card-title text-uppercase text-muted mb-0 text-white">
                Notifications
              </h5>

              <span class=" h2 font-weight-bold mb-0 text-white"> 50/62 </span>

              <div class=" progress progress-xs mt-3 mb-0">
                <progressbar type="success" [value]="90"> </progressbar>
              </div>
            </div>

            <div class=" col-auto">
              <div dropdown class="dropdown no-caret">
                <button
                  id="button-basic"
                  dropdownToggle
                  type="button"
                  class="btn btn-sm btn-neutral mr-0 dropdown-toggle"
                  aria-controls="dropdown-basic"
                >
                  <i class=" fas fa-ellipsis-h"> </i>
                </button>

                <div
                  class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                  *dropdownMenu
                >
                  <a class="dropdown-item" href="javascript:void(0)">
                    Action
                  </a>

                  <a class="dropdown-item" href="javascript:void(0)">
                    Another action
                  </a>

                  <a class="dropdown-item" href="javascript:void(0)">
                    Something else here
                  </a>
                </div>
              </div>
            </div>
          </div>

          <p class=" mt-3 mb-0 text-sm">
            <a
              class=" text-nowrap text-white font-weight-600"
              href="javascript:void(0)"
            >
              See details
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class=" card-deck flex-column flex-xl-row">
    <div class=" card">
      <div class=" card-header bg-transparent">
        <h6 class=" text-muted text-uppercase ls-1 mb-1">Overview</h6>

        <h2 class=" h3 mb-0">Sales value</h2>
      </div>

      <div class=" card-body">
        <div class=" chart">
          <canvas class=" chart-canvas" id="chart-sales"> </canvas>
        </div>
      </div>
    </div>

    <div class=" card">
      <div class=" card-header bg-transparent">
        <div class=" row align-items-center">
          <div class=" col">
            <h6 class=" text-uppercase text-muted ls-1 mb-1">Performance</h6>

            <h2 class=" h3 mb-0">Total orders</h2>
          </div>
        </div>
      </div>

      <div class=" card-body">
        <div class=" chart">
          <canvas class=" chart-canvas" id="chart-bars1"> </canvas>
        </div>
      </div>
    </div>

    <div class=" card">
      <div class=" card-header">
        <div class=" row align-items-center">
          <div class=" col-8">
            <h6 class=" surtitle">5/23 projects</h6>

            <h5 class=" h3 mb-0">Progress track</h5>
          </div>

          <div class=" col-4 text-right">
            <a class=" btn btn-sm btn-neutral" href="javascript:void(0)">
              Action
            </a>
          </div>
        </div>
      </div>

      <div class=" card-body">
        <ul class=" list-group list-group-flush list my--3">
          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/bootstrap.jpg"
                  />
                </a>
              </div>

              <div class=" col">
                <h5>Argon Design System</h5>

                <div class=" progress progress-xs mb-0">
                  <progressbar type="warning" [value]="60"> </progressbar>
                </div>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/angular.jpg"
                  />
                </a>
              </div>

              <div class=" col">
                <h5>Angular Now UI Kit PRO</h5>

                <div class=" progress progress-xs mb-0">
                  <progressbar type="success" [value]="100"> </progressbar>
                </div>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/sketch.jpg"
                  />
                </a>
              </div>

              <div class=" col">
                <h5>Black Dashboard</h5>

                <div class=" progress progress-xs mb-0">
                  <progressbar type="danger" [value]="72"> </progressbar>
                </div>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/react.jpg"
                  />
                </a>
              </div>

              <div class=" col">
                <h5>React Material Dashboard</h5>

                <div class=" progress progress-xs mb-0">
                  <progressbar type="info" [value]="90"> </progressbar>
                </div>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/vue.jpg"
                  />
                </a>
              </div>

              <div class=" col">
                <h5>Vue Paper UI Kit PRO</h5>

                <div class=" progress progress-xs mb-0">
                  <progressbar type="success" [value]="100"> </progressbar>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <div class=" row">
    <div class=" col-xl-8">
      <div class=" card">
        <div class=" card-header border-0">
          <div class=" row align-items-center">
            <div class=" col"><h3 class=" mb-0">Page visits</h3></div>

            <div class=" col text-right">
              <a class=" btn btn-sm btn-primary" href="javascript:void(0)">
                See all
              </a>
            </div>
          </div>
        </div>

        <div class=" table-responsive">
          <table class=" table align-items-center table-flush">
            <thead class=" thead-light">
              <tr>
                <th class=" sort" data-sort="name" scope="col">Project</th>

                <th class=" sort" data-sort="budget" scope="col">Budget</th>

                <th class=" sort" data-sort="status" scope="col">Status</th>

                <th scope="col">Users</th>

                <th class=" sort" data-sort="completion" scope="col">
                  Completion
                </th>

                <th scope="col"></th>
              </tr>
            </thead>

            <tbody class=" list">
              <tr>
                <th scope="row">
                  <div class=" media align-items-center">
                    <a
                      class=" avatar rounded-circle mr-3"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/bootstrap.jpg"
                      />
                    </a>

                    <div class=" media-body">
                      <span class=" name mb-0 text-sm">
                        Argon Design System
                      </span>
                    </div>
                  </div>
                </th>

                <td class=" budget">$2500 USD</td>

                <td>
                  <span class=" badge badge-dot mr-4">
                    <i class=" bg-warning"> </i>

                    <span class=" status"> pending </span>
                  </span>
                </td>

                <td>
                  <div class=" avatar-group">
                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Ryan Thompson"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-1.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Romina Hadid"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-2.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Alexander Smith"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-3.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Jessica Doe"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-4.jpg"
                      />
                    </a>
                  </div>
                </td>

                <td>
                  <div class=" d-flex align-items-center">
                    <span class=" completion mr-2"> 60% </span>

                    <div>
                      <div class=" progress">
                        <progressbar type="warning" [value]="60"> </progressbar>
                      </div>
                    </div>
                  </div>
                </td>

                <td class=" text-right">
                  <div dropdown class="dropdown no-caret">
                    <a
                      id="button-basic"
                      dropdownToggle
                      role="button"
                      class="btn btn-sm btn-icon-only text-light dropdown-toggle"
                      aria-controls="dropdown-basic"
                    >
                      <i class=" fas fa-ellipsis-v"> </i>
                    </a>

                    <div
                      class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                      *dropdownMenu
                    >
                      <a class="dropdown-item" href="javascript:void(0)">
                        Action
                      </a>

                      <a class="dropdown-item" href="javascript:void(0)">
                        Another action
                      </a>

                      <a class="dropdown-item" href="javascript:void(0)">
                        Something else here
                      </a>
                    </div>
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="row">
                  <div class=" media align-items-center">
                    <a
                      class=" avatar rounded-circle mr-3"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/angular.jpg"
                      />
                    </a>

                    <div class=" media-body">
                      <span class=" name mb-0 text-sm">
                        Angular Now UI Kit PRO
                      </span>
                    </div>
                  </div>
                </th>

                <td class=" budget">$1800 USD</td>

                <td>
                  <span class=" badge badge-dot mr-4">
                    <i class=" bg-success"> </i>

                    <span class=" status"> completed </span>
                  </span>
                </td>

                <td>
                  <div class=" avatar-group">
                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Ryan Thompson"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-1.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Romina Hadid"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-2.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Alexander Smith"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-3.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Jessica Doe"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-4.jpg"
                      />
                    </a>
                  </div>
                </td>

                <td>
                  <div class=" d-flex align-items-center">
                    <span class=" completion mr-2"> 100% </span>

                    <div>
                      <div class=" progress">
                        <progressbar type="success" [value]="100">
                        </progressbar>
                      </div>
                    </div>
                  </div>
                </td>

                <td class=" text-right">
                  <div dropdown class="dropdown no-caret">
                    <a
                      id="button-basic"
                      dropdownToggle
                      role="button"
                      class="btn btn-sm btn-icon-only text-light dropdown-toggle"
                      aria-controls="dropdown-basic"
                    >
                      <i class=" fas fa-ellipsis-v"> </i>
                    </a>

                    <div
                      class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                      *dropdownMenu
                    >
                      <a href="javascript:void(0)" class="dropdown-item">
                        Action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Another action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Something else here
                      </a>
                    </div>
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="row">
                  <div class=" media align-items-center">
                    <a
                      class=" avatar rounded-circle mr-3"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/sketch.jpg"
                      />
                    </a>

                    <div class=" media-body">
                      <span class=" name mb-0 text-sm"> Black Dashboard </span>
                    </div>
                  </div>
                </th>

                <td class=" budget">$3150 USD</td>

                <td>
                  <span class=" badge badge-dot mr-4">
                    <i class=" bg-danger"> </i>

                    <span class=" status"> delayed </span>
                  </span>
                </td>

                <td>
                  <div class=" avatar-group">
                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Ryan Thompson"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-1.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Romina Hadid"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-2.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Alexander Smith"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-3.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Jessica Doe"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-4.jpg"
                      />
                    </a>
                  </div>
                </td>

                <td>
                  <div class=" d-flex align-items-center">
                    <span class=" completion mr-2"> 72% </span>

                    <div>
                      <div class=" progress">
                        <progressbar type="danger" [value]="72"> </progressbar>
                      </div>
                    </div>
                  </div>
                </td>

                <td class=" text-right">
                  <div dropdown class="dropdown no-caret">
                    <a
                      id="button-basic"
                      dropdownToggle
                      role="button"
                      class="btn btn-sm btn-icon-only text-light dropdown-toggle"
                      aria-controls="dropdown-basic"
                    >
                      <i class=" fas fa-ellipsis-v"> </i>
                    </a>

                    <div
                      class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                      *dropdownMenu
                    >
                      <a href="javascript:void(0)" class="dropdown-item">
                        Action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Another action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Something else here
                      </a>
                    </div>
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="row">
                  <div class=" media align-items-center">
                    <a
                      class=" avatar rounded-circle mr-3"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/react.jpg"
                      />
                    </a>

                    <div class=" media-body">
                      <span class=" name mb-0 text-sm">
                        React Material Dashboard
                      </span>
                    </div>
                  </div>
                </th>

                <td class=" budget">$4400 USD</td>

                <td>
                  <span class=" badge badge-dot mr-4">
                    <i class=" bg-info"> </i>

                    <span class=" status"> on schedule </span>
                  </span>
                </td>

                <td>
                  <div class=" avatar-group">
                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Ryan Thompson"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-1.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Romina Hadid"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-2.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Alexander Smith"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-3.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Jessica Doe"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-4.jpg"
                      />
                    </a>
                  </div>
                </td>

                <td>
                  <div class=" d-flex align-items-center">
                    <span class=" completion mr-2"> 90% </span>

                    <div>
                      <div class=" progress">
                        <progressbar type="info" [value]="90"> </progressbar>
                      </div>
                    </div>
                  </div>
                </td>

                <td class=" text-right">
                  <div dropdown class="dropdown no-caret">
                    <a
                      id="button-basic"
                      dropdownToggle
                      role="button"
                      class="btn btn-sm btn-icon-only text-light dropdown-toggle"
                      aria-controls="dropdown-basic"
                    >
                      <i class=" fas fa-ellipsis-v"> </i>
                    </a>

                    <div
                      class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                      *dropdownMenu
                    >
                      <a href="javascript:void(0)" class="dropdown-item">
                        Action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Another action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Something else here
                      </a>
                    </div>
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="row">
                  <div class=" media align-items-center">
                    <a
                      class=" avatar rounded-circle mr-3"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/vue.jpg"
                      />
                    </a>

                    <div class=" media-body">
                      <span class=" name mb-0 text-sm">
                        Vue Paper UI Kit PRO
                      </span>
                    </div>
                  </div>
                </th>

                <td class=" budget">$2200 USD</td>

                <td>
                  <span class=" badge badge-dot mr-4">
                    <i class=" bg-success"> </i>

                    <span class=" status"> completed </span>
                  </span>
                </td>

                <td>
                  <div class=" avatar-group">
                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Ryan Thompson"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-1.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Romina Hadid"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-2.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Alexander Smith"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-3.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Jessica Doe"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-4.jpg"
                      />
                    </a>
                  </div>
                </td>

                <td>
                  <div class=" d-flex align-items-center">
                    <span class=" completion mr-2"> 100% </span>

                    <div>
                      <div class=" progress">
                        <progressbar type="success" [value]="100">
                        </progressbar>
                      </div>
                    </div>
                  </div>
                </td>

                <td class=" text-right">
                  <div dropdown class="dropdown no-caret" [dropup]="'true'">
                    <a
                      id="button-dropup"
                      dropdownToggle
                      role="button"
                      class="btn btn-sm btn-icon-only text-light dropdown-toggle"
                      aria-controls="dropdown-dropup"
                    >
                      <i class=" fas fa-ellipsis-v"> </i>
                    </a>

                    <div
                      class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                      *dropdownMenu
                    >
                      <a href="javascript:void(0)" class="dropdown-item">
                        Action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Another action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Something else here
                      </a>
                    </div>
                  </div>
                </td>
              </tr>

              <tr>
                <th scope="row">
                  <div class=" media align-items-center">
                    <a
                      class=" avatar rounded-circle mr-3"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/bootstrap.jpg"
                      />
                    </a>

                    <div class=" media-body">
                      <span class=" name mb-0 text-sm">
                        Argon Design System
                      </span>
                    </div>
                  </div>
                </th>

                <td class=" budget">$2500 USD</td>

                <td>
                  <span class=" badge badge-dot mr-4">
                    <i class=" bg-warning"> </i>

                    <span class=" status"> pending </span>
                  </span>
                </td>

                <td>
                  <div class=" avatar-group">
                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Ryan Thompson"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-1.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Romina Hadid"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-2.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Alexander Smith"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-3.jpg"
                      />
                    </a>

                    <a
                      class=" avatar avatar-sm rounded-circle"
                      tooltip="Jessica Doe"
                      placement="top"
                      href="javascript:void(0)"
                    >
                      <img
                        alt="Image placeholder"
                        src="assets/img/theme/team-4.jpg"
                      />
                    </a>
                  </div>
                </td>

                <td>
                  <div class=" d-flex align-items-center">
                    <span class=" completion mr-2"> 60% </span>

                    <div>
                      <div class=" progress">
                        <progressbar type="warning" [value]="60"> </progressbar>
                      </div>
                    </div>
                  </div>
                </td>

                <td class=" text-right">
                  <div dropdown class="dropdown no-caret" [dropup]="'true'">
                    <a
                      id="button-dropup"
                      dropdownToggle
                      role="button"
                      class="btn btn-sm btn-icon-only text-light dropdown-toggle"
                      aria-controls="dropdown-dropup"
                    >
                      <i class=" fas fa-ellipsis-v"> </i>
                    </a>

                    <div
                      class="dropdown-menu dropdown-menu-right dropdown-menu-arrow"
                      *dropdownMenu
                    >
                      <a href="javascript:void(0)" class="dropdown-item">
                        Action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Another action
                      </a>

                      <a href="javascript:void(0)" class="dropdown-item">
                        Something else here
                      </a>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class=" col-xl-4">
      <div class=" card widget-calendar">
        <div class=" card-header">
          <div class=" row align-items-center">
            <div class=" col-8"><h5 class=" h3 mb-0">Real time</h5></div>

            <div class=" col-4 text-right">
              <a class=" btn btn-sm btn-neutral" href="javascript:void(0)">
                Action
              </a>
            </div>
          </div>
        </div>

        <div class=" card-body">
          <app-vector-map-component></app-vector-map-component>
    
          <ul class=" list-group list-group-flush list my--3">
            <li class=" list-group-item px-0">
              <div class=" row align-items-center">
                <div class=" col-auto">
                  <img
                    alt="Country flag"
                    src="assets/img/icons/flags/US.png"
                  />
                </div>

                <div class=" col">
                  <small> Country: </small>

                  <h5 class=" mb-0">United States</h5>
                </div>

                <div class=" col">
                  <small> Visits: </small>

                  <h5 class=" mb-0">2500</h5>
                </div>

                <div class=" col">
                  <small> Bounce: </small>

                  <h5 class=" mb-0">30%</h5>
                </div>
              </div>
            </li>

            <li class=" list-group-item px-0">
              <div class=" row align-items-center">
                <div class=" col-auto">
                  <img
                    alt="Country flag"
                    src="assets/img/icons/flags/DE.png"
                  />
                </div>

                <div class=" col">
                  <small> Country: </small>

                  <h5 class=" mb-0">Germany</h5>
                </div>

                <div class=" col">
                  <small> Visits: </small>

                  <h5 class=" mb-0">2500</h5>
                </div>

                <div class=" col">
                  <small> Bounce: </small>

                  <h5 class=" mb-0">30%</h5>
                </div>
              </div>
            </li>

            <li class=" list-group-item px-0">
              <div class=" row align-items-center">
                <div class=" col-auto">
                  <img
                    alt="Country flag"
                    src="assets/img/icons/flags/GB.png"
                  />
                </div>

                <div class=" col">
                  <small> Country: </small>

                  <h5 class=" mb-0">Great Britain</h5>
                </div>

                <div class=" col">
                  <small> Visits: </small>

                  <h5 class=" mb-0">2500</h5>
                </div>

                <div class=" col">
                  <small> Bounce: </small>

                  <h5 class=" mb-0">30%</h5>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <div class=" card-deck flex-column flex-xl-row">
    <div class=" card">
      <div class=" card-header"><h5 class=" h3 mb-0">Team members</h5></div>

      <div class=" card-body">
        <ul class=" list-group list-group-flush list my--3">
          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/team-1.jpg"
                  />
                </a>
              </div>

              <div class=" col ml--2">
                <h4 class=" mb-0">
                  <a href="javascript:void(0)"> John Michael </a>
                </h4>

                <span class=" text-success"> ● </span>

                <small> Online </small>
              </div>

              <div class=" col-auto">
                <button class=" btn btn-sm btn-primary" type="button">
                  Add
                </button>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/team-2.jpg"
                  />
                </a>
              </div>

              <div class=" col ml--2">
                <h4 class=" mb-0">
                  <a href="javascript:void(0)"> Alex Smith </a>
                </h4>

                <span class=" text-warning"> ● </span>

                <small> In a meeting </small>
              </div>

              <div class=" col-auto">
                <button class=" btn btn-sm btn-primary" type="button">
                  Add
                </button>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/team-3.jpg"
                  />
                </a>
              </div>

              <div class=" col ml--2">
                <h4 class=" mb-0">
                  <a href="javascript:void(0)"> Samantha Ivy </a>
                </h4>

                <span class=" text-danger"> ● </span>

                <small> Offline </small>
              </div>

              <div class=" col-auto">
                <button class=" btn btn-sm btn-primary" type="button">
                  Add
                </button>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/team-4.jpg"
                  />
                </a>
              </div>

              <div class=" col ml--2">
                <h4 class=" mb-0">
                  <a href="javascript:void(0)"> John Michael </a>
                </h4>

                <span class=" text-success"> ● </span>

                <small> Online </small>
              </div>

              <div class=" col-auto">
                <button class=" btn btn-sm btn-primary" type="button">
                  Add
                </button>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class=" card">
      <div class=" card-header"><h5 class=" h3 mb-0">To do list</h5></div>

      <div class=" card-body p-0">
        <ul class=" list-group list-group-flush" data-toggle="checklist">
          <li
            class=" checklist-entry list-group-item flex-column align-items-start py-4 px-4"
          >
            <div class=" checklist-item checklist-item-success">
              <div class=" checklist-info">
                <h5 class=" checklist-title mb-0">Call with Dave</h5>

                <small> 10:30 AM </small>
              </div>

              <div>
                <div
                  class=" custom-control custom-checkbox custom-checkbox-success"
                >
                  <input
                    checked="checked"
                    class=" custom-control-input"
                    id="chk-todo-task-1"
                    type="checkbox"
                  />

                  <label class=" custom-control-label" for="chk-todo-task-1">
                  </label>
                </div>
              </div>
            </div>
          </li>

          <li
            class=" checklist-entry list-group-item flex-column align-items-start py-4 px-4"
          >
            <div class=" checklist-item checklist-item-warning">
              <div class=" checklist-info">
                <h5 class=" checklist-title mb-0">Lunch meeting</h5>

                <small> 10:30 AM </small>
              </div>

              <div>
                <div
                  class=" custom-control custom-checkbox custom-checkbox-warning"
                >
                  <input
                    class=" custom-control-input"
                    id="chk-todo-task-2"
                    type="checkbox"
                  />

                  <label class=" custom-control-label" for="chk-todo-task-2">
                  </label>
                </div>
              </div>
            </div>
          </li>

          <li
            class=" checklist-entry list-group-item flex-column align-items-start py-4 px-4"
          >
            <div class=" checklist-item checklist-item-info">
              <div class=" checklist-info">
                <h5 class=" checklist-title mb-0">Argon Dashboard Launch</h5>

                <small> 10:30 AM </small>
              </div>

              <div>
                <div
                  class=" custom-control custom-checkbox custom-checkbox-info"
                >
                  <input
                    class=" custom-control-input"
                    id="chk-todo-task-3"
                    type="checkbox"
                  />

                  <label class=" custom-control-label" for="chk-todo-task-3">
                  </label>
                </div>
              </div>
            </div>
          </li>

          <li
            class=" checklist-entry list-group-item flex-column align-items-start py-4 px-4"
          >
            <div class=" checklist-item checklist-item-danger">
              <div class=" checklist-info">
                <h5 class=" checklist-title mb-0">Winter Hackaton</h5>

                <small> 10:30 AM </small>
              </div>

              <div>
                <div
                  class=" custom-control custom-checkbox custom-checkbox-danger"
                >
                  <input
                    checked="checked"
                    class=" custom-control-input"
                    id="chk-todo-task-4"
                    type="checkbox"
                  />

                  <label class=" custom-control-label" for="chk-todo-task-4">
                  </label>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class=" card">
      <div class=" card-header"><h5 class=" h3 mb-0">Progress track</h5></div>

      <div class=" card-body">
        <ul class=" list-group list-group-flush list my--3">
          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/bootstrap.jpg"
                  />
                </a>
              </div>

              <div class=" col">
                <h5>Argon Design System</h5>

                <div class=" progress progress-xs mb-0">
                  <progressbar type="warning" [value]="60"> </progressbar>
                </div>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/angular.jpg"
                  />
                </a>
              </div>

              <div class=" col">
                <h5>Angular Now UI Kit PRO</h5>

                <div class=" progress progress-xs mb-0">
                  <progressbar type="success" [value]="100"> </progressbar>
                </div>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/sketch.jpg"
                  />
                </a>
              </div>

              <div class=" col">
                <h5>Black Dashboard</h5>

                <div class=" progress progress-xs mb-0">
                  <progressbar type="danger" [value]="72"> </progressbar>
                </div>
              </div>
            </div>
          </li>

          <li class=" list-group-item px-0">
            <div class=" row align-items-center">
              <div class=" col-auto">
                <a class=" avatar rounded-circle" href="javascript:void(0)">
                  <img
                    alt="Image placeholder"
                    src="assets/img/theme/react.jpg"
                  />
                </a>
              </div>

              <div class=" col">
                <h5>React Material Dashboard</h5>

                <div class=" progress progress-xs mb-0">
                  <progressbar type="info" [value]="90"> </progressbar>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
