import { Component, OnInit } from '@angular/core';
import { Course } from 'src/app/core/models/Course';
import { CourseService } from 'src/app/core/services/course.service';

@Component({
  selector: 'app-courses-table',
  templateUrl: './courses.component.html',
  styleUrls: ['./courses.component.scss']
})
export class CoursesTableComponent implements OnInit {
  courses: Course[] = [];
  isLoading: boolean = false;
  errorMessage: string = '';
  
  // Modal states
  showAddModal: boolean = false;
  showEditModal: boolean = false;
  showDeleteModal: boolean = false;
  selectedCourse: Course | null = null;

  // Form data
  courseForm = {
    title: '',
    description: '',
    instructor: '',
    duration: '',
    level: 'BEGINNER',
    category: '',
    price: 0,
    maxStudents: 50,
    status: 'ACTIVE',
    image: null as File | null
  };

  levels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'];
  statuses = ['ACTIVE', 'INACTIVE', 'DRAFT'];
  categories = ['Programming', 'Design', 'Business', 'Marketing', 'Data Science', 'Languages', 'Other'];

  constructor(private courseService: CourseService) {}

  ngOnInit(): void {
    this.loadCourses();
  }

  loadCourses(): void {
    this.isLoading = true;
    this.courseService.getAllCourses().subscribe({
      next: (data) => {
        this.courses = data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to load courses:', error);
        this.errorMessage = 'Failed to load courses. Please try again.';
        this.isLoading = false;
      }
    });
  }

  // Modal methods
  openAddModal(): void {
    this.resetForm();
    this.showAddModal = true;
  }

  openEditModal(course: Course): void {
    this.selectedCourse = course;
    this.courseForm = {
      title: course.titre,
      description: course.description,

      duration: course.duree,
      level: course.niveau,
      category: course.categorie,
      price: course.prix,
      maxStudents: course.nb_max_etudiant || 50,
        instructor: '',
      status: 'ACTIVE',
      image: null
    };
    this.showEditModal = true;
  }

  openDeleteModal(course: Course): void {
    this.selectedCourse = course;
    this.showDeleteModal = true;
  }

  closeModals(): void {
    this.showAddModal = false;
    this.showEditModal = false;
    this.showDeleteModal = false;
    this.selectedCourse = null;
    this.resetForm();
  }

  resetForm(): void {
    this.courseForm = {
      title: '',
      description: '',
      instructor: '',
      duration: '',
      level: 'BEGINNER',
      category: '',
      price: 0,
      maxStudents: 50,
      status: 'ACTIVE',
      image: null
    };
  }

  // CRUD operations
  addCourse(): void {
    if (!this.validateForm()) return;

    this.isLoading = true;
    this.courseService.addCourse(
      this.courseForm.title,
      this.courseForm.description,
      this.courseForm.instructor,
      this.courseForm.duration,
      this.courseForm.level,
      this.courseForm.category,
      this.courseForm.price,
      this.courseForm.maxStudents,
      this.courseForm.status,
      this.courseForm.image
    ).subscribe({
      next: () => {
        this.loadCourses();
        this.closeModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to add course:', error);
        this.errorMessage = 'Failed to add course. Please try again.';
        this.isLoading = false;
      }
    });
  }

  updateCourse(): void {
    if (!this.selectedCourse || !this.validateForm()) return;

    this.isLoading = true;
    this.courseService.updateCourse(
      this.selectedCourse.id_course,
      this.courseForm.title,
      this.courseForm.description,
      this.courseForm.instructor,
      this.courseForm.duration,
      this.courseForm.level,
      this.courseForm.category,
      this.courseForm.price,
      this.courseForm.maxStudents,
      this.courseForm.status,
      this.courseForm.image
    ).subscribe({
      next: () => {
        this.loadCourses();
        this.closeModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to update course:', error);
        this.errorMessage = 'Failed to update course. Please try again.';
        this.isLoading = false;
      }
    });
  }

  deleteCourse(): void {
    if (!this.selectedCourse) return;

    this.isLoading = true;
    this.courseService.deleteCourse(this.selectedCourse.id_course).subscribe({
      next: () => {
        this.loadCourses();
        this.closeModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to delete course:', error);
        this.errorMessage = 'Failed to delete course. Please try again.';
        this.isLoading = false;
      }
    });
  }

  // Utility methods
  validateForm(): boolean {
    return !!(this.courseForm.title && 
             this.courseForm.description && 
             this.courseForm.instructor && 
             this.courseForm.duration && 
             this.courseForm.category);
  }

  onImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.courseForm.image = file;
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'ACTIVE': return 'badge-success';
      case 'INACTIVE': return 'badge-danger';
      case 'DRAFT': return 'badge-warning';
      default: return 'badge-secondary';
    }
  }

  getLevelBadgeClass(level: string): string {
    switch (level) {
      case 'BEGINNER': return 'badge-info';
      case 'INTERMEDIATE': return 'badge-warning';
      case 'ADVANCED': return 'badge-danger';
      default: return 'badge-secondary';
    }
  }

  formatPrice(price: number): string {
    return price === 0 ? 'Free' : `$${price}`;
  }
}
