import { Component, OnInit } from '@angular/core';
import { Course } from 'src/app/core/models/Course';
import { CourseService } from 'src/app/core/services/course.service';
import { Category } from 'src/app/core/models/Category';
import { CategoryService } from 'src/app/core/services/category.service';

@Component({
  selector: 'app-courses-table',
  templateUrl: './courses.component.html',
  styleUrls: ['./courses.component.scss']
})
export class CoursesTableComponent implements OnInit {
  today: Date = new Date();

  courses: Course[] = [];
  isLoading: boolean = false;
  errorMessage: string = '';

  // Modal states
  showAddModal: boolean = false;
  showEditModal: boolean = false;
  showDeleteModal: boolean = false;
  selectedCourse: Course | null = null;

  // Category management
  categoriesList: Category[] = [];
  showCategoryModal: boolean = false;
  showEditCategoryModal: boolean = false;
  showDeleteCategoryModal: boolean = false;
  selectedCategory: Category | null = null;
  categoryForm = {
    title: ''
  };

  // Form data
  courseForm = {
    titre: '',
    description: '',
    duree: '',
    niveau: 'BEGINNER',
    categorieId: null as number | null, // Store category ID instead of name
    prix: 0,
    nbMaxEtudiant: 50, // Default value to ensure it's never null/undefined
    format: 'VIDEO',
    image: null as File | null,
    video: null as File | null,
    fichier: null as File | null
  };

  levels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'];
  formats = ['VIDEO', 'PDF', 'MIXED', 'LIVE'];
  categories = ['Programming', 'Design', 'Business', 'Marketing', 'Data Science', 'Languages', 'Other'];

  constructor(
    private courseService: CourseService,
    private categoryService: CategoryService
  ) {}

  ngOnInit(): void {
    this.loadCourses();
    this.loadCategories();
  }

  loadCourses(): void {
    this.isLoading = true;
    this.courseService.getAllCourses().subscribe({
      next: (data) => {
        this.courses = data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to load courses:', error);
        this.errorMessage = 'Failed to load courses. Please try again.';
        this.isLoading = false;
      }
    });
  }

  loadCategories(): void {
    this.categoryService.getAllCategories().subscribe({
      next: (data) => {
        this.categoriesList = data;
        // No need to update categories array anymore - we'll use categoriesList directly
      },
      error: (error) => {
        console.error('Failed to load categories:', error);
      }
    });
  }

  // Modal methods
  openAddModal(): void {
    this.resetForm();
    this.showAddModal = true;
  }

  openEditModal(course: Course): void {
    this.selectedCourse = course;
    this.courseForm = {
      titre: course.titre,
      description: course.description,
      duree: course.duree,
      niveau: course.niveau,
     categorie: {
        id: this.courseForm.categorieId, 
        title: ''
    },
      // <- Use the category ID from the form
  
      prix: course.prix,
      nbMaxEtudiant: course.nb_max_etudiant || 50, // Ensure we always have a valid value
      format: course.format || 'VIDEO',
      image: null,
      video: null,
      fichier: null
    };
    this.showEditModal = true;
  }

  openDeleteModal(course: Course): void {
    this.selectedCourse = course;
    this.showDeleteModal = true;
  }

  closeModals(): void {
    this.showAddModal = false;
    this.showEditModal = false;
    this.showDeleteModal = false;
    this.selectedCourse = null;
    this.resetForm();
  }

  // Category modal methods
  openCategoryModal(): void {
    this.resetCategoryForm();
    this.showCategoryModal = true;
  }

  openEditCategoryModal(category: Category): void {
    this.selectedCategory = category;
    this.categoryForm.title = category.title || '';
    this.showEditCategoryModal = true;
  }

  openDeleteCategoryModal(category: Category): void {
    this.selectedCategory = category;
    this.showDeleteCategoryModal = true;
  }

  closeCategoryModals(): void {
    this.showCategoryModal = false;
    this.showEditCategoryModal = false;
    this.showDeleteCategoryModal = false;
    this.selectedCategory = null;
    this.resetCategoryForm();
  }

  resetCategoryForm(): void {
    this.categoryForm.title = '';
  }

  resetForm(): void {
    this.courseForm = {
      titre: '',
      description: '',
      duree: '',
      niveau: 'BEGINNER',
      categorieId: null,
      prix: 0,
      nbMaxEtudiant: 50, // Always ensure a default value
      format: 'VIDEO',
      image: null,
      video: null,
      fichier: null
    };
  }

  // CRUD operations
  addCourse(): void {
  if (!this.validateForm()) return;

  if (!this.courseForm.image) {
    this.errorMessage = 'Please select an image for the course.';
    return;
  }

  this.isLoading = true;

  // 1. Build the Course object
  const cours: Course = {
    id_cours: null,
    titre: this.courseForm.titre,
    description: this.courseForm.description,
    duree: this.courseForm.duree,
    niveau: this.courseForm.niveau,
    categorie: {
      id: this.courseForm.categorieId , 
      title: ''
    },
    prix: this.courseForm.prix,
    nb_max_etudiant: this.courseForm.nbMaxEtudiant,
    date_publication: new Date(),
    format: this.courseForm.format,
    image:'',
    fichier:'',
    video:''

  };

  // 2. Extract files
  const imageFile: File = this.courseForm.image;
  const fichierFile: File | undefined = this.courseForm.fichier;
  const videoFile: File | undefined = this.courseForm.video;
  console.log("cours", cours);
  // 3. Call the service
  this.courseService.addCours(cours, imageFile, fichierFile, videoFile).subscribe({
    next: () => {
      this.loadCourses();
      this.closeModals();
      this.isLoading = false;
    },
    error: (error) => {
      console.error('Failed to add course:', error);
      this.errorMessage = 'Failed to add course. Please try again.';
      this.isLoading = false;
    }
  });
}


  updateCourse(): void {
    if (!this.selectedCourse || !this.validateForm()) return;

    this.isLoading = true;
    this.courseService.updateCourse(
      this.selectedCourse.id_cours,
      this.courseForm.titre,
      this.courseForm.description,
      this.courseForm.duree,
      this.courseForm.niveau,
      this.courseForm.categorieId!,
      this.courseForm.prix,
      this.courseForm.nbMaxEtudiant,
      this.courseForm.format,
      this.courseForm.image,
      this.courseForm.video,
      this.courseForm.fichier
    ).subscribe({
      next: () => {
        this.loadCourses();
        this.closeModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to update course:', error);
        this.errorMessage = 'Failed to update course. Please try again.';
        this.isLoading = false;
      }
    });
  }

  deleteCourse(): void {
    if (!this.selectedCourse) return;

    this.isLoading = true;
    this.courseService.deleteCourse(this.selectedCourse.id_cours).subscribe({
      next: () => {
        this.loadCourses();
        this.closeModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to delete course:', error);
        this.errorMessage = 'Failed to delete course. Please try again.';
        this.isLoading = false;
      }
    });
  }

  // Utility methods
  validateForm(): boolean {
    return !!(this.courseForm.titre &&
             this.courseForm.description &&
             this.courseForm.duree &&
             this.courseForm.categorieId &&
             this.courseForm.nbMaxEtudiant > 0); // Ensure max students is positive
  }

  onImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.courseForm.image = file;
    }
  }

  onVideoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.courseForm.video = file;
    }
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.courseForm.fichier = file;
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'ACTIVE': return 'badge-success';
      case 'INACTIVE': return 'badge-danger';
      case 'DRAFT': return 'badge-warning';
      default: return 'badge-secondary';
    }
  }

  getLevelBadgeClass(level: string): string {
    switch (level) {
      case 'BEGINNER': return 'badge-info';
      case 'INTERMEDIATE': return 'badge-warning';
      case 'ADVANCED': return 'badge-danger';
      default: return 'badge-secondary';
    }
  }

  formatPrice(price: number): string {
    return price === 0 ? 'Free' : `$${price}`;
  }

  // Category CRUD operations
  addCategory(): void {
    if (!this.categoryForm.title.trim()) {
      this.errorMessage = 'Category title is required.';
      return;
    }
    console.log("category", this.categoryForm.title);
    this.isLoading = true;
    this.categoryService.addCategory(this.categoryForm.title).subscribe({
      next: () => {
        this.loadCategories();
        this.closeCategoryModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to add category:', error);
        this.errorMessage = 'Failed to add category. Please try again.';
        this.isLoading = false;
      }
    });
  }

  updateCategory(): void {
    if (!this.selectedCategory || !this.categoryForm.title.trim()) {
     
      this.errorMessage = 'Category title is required.';
      return;
    }

    console.log("selected category:", this.selectedCategory.id, this.categoryForm.title);
    this.isLoading = true;
    
    this.categoryService.updateCategory(this.selectedCategory.id!, this.categoryForm.title).subscribe({
      next: () => {
        this.loadCategories();
        this.closeCategoryModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to update category:', error);
        this.errorMessage = 'Failed to update category. Please try again.';
        this.isLoading = false;
      }
    });
  }

  deleteCategory(): void {
    if (!this.selectedCategory) return;

    this.isLoading = true;
    this.categoryService.deleteCategory(this.selectedCategory.id!).subscribe({
      next: () => {
        this.loadCategories();
        this.closeCategoryModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to delete category:', error);
        this.errorMessage = 'Failed to delete category. Please try again.';
        this.isLoading = false;
      }
    });
  }
}
