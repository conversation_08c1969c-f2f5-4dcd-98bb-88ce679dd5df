import { Component, OnInit } from '@angular/core';
import { Course } from 'src/app/core/models/Course';
import { CourseService } from 'src/app/core/services/course.service';

@Component({
  selector: 'app-courses-table',
  templateUrl: './courses.component.html',
  styleUrls: ['./courses.component.scss']
})
export class CoursesTableComponent implements OnInit {
  courses: Course[] = [];
  isLoading: boolean = false;
  errorMessage: string = '';
  
  // Modal states
  showAddModal: boolean = false;
  showEditModal: boolean = false;
  showDeleteModal: boolean = false;
  selectedCourse: Course | null = null;

  // Form data
  courseForm = {
    titre: '',
    description: '',
    duree: '',
    niveau: 'BEGINNER',
    categorie: '',
    prix: 0,
    nbMaxEtudiant: 50,
    format: 'VIDEO',
    image: null as File | null,
    video: null as File | null,
    fichier: null as File | null
  };

  levels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED'];
  formats = ['VIDEO', 'PDF', 'MIXED', 'LIVE'];
  categories = ['Programming', 'Design', 'Business', 'Marketing', 'Data Science', 'Languages', 'Other'];

  constructor(private courseService: CourseService) {}

  ngOnInit(): void {
    this.loadCourses();
  }

  loadCourses(): void {
    this.isLoading = true;
    this.courseService.getAllCourses().subscribe({
      next: (data) => {
        this.courses = data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to load courses:', error);
        this.errorMessage = 'Failed to load courses. Please try again.';
        this.isLoading = false;
      }
    });
  }

  // Modal methods
  openAddModal(): void {
    this.resetForm();
    this.showAddModal = true;
  }

  openEditModal(course: Course): void {
    this.selectedCourse = course;
    this.courseForm = {
      titre: course.titre,
      description: course.description,
      duree: course.duree,
      niveau: course.niveau,
      categorie: course.categorie,
      prix: course.prix,
      nbMaxEtudiant: course.nb_max_etudiant || 50,
      format: course.format || 'VIDEO',
      image: null,
      video: null,
      fichier: null
    };
    this.showEditModal = true;
  }

  openDeleteModal(course: Course): void {
    this.selectedCourse = course;
    this.showDeleteModal = true;
  }

  closeModals(): void {
    this.showAddModal = false;
    this.showEditModal = false;
    this.showDeleteModal = false;
    this.selectedCourse = null;
    this.resetForm();
  }

  resetForm(): void {
    this.courseForm = {
      titre: '',
      description: '',
      duree: '',
      niveau: 'BEGINNER',
      categorie: '',
      prix: 0,
      nbMaxEtudiant: 50,
      format: 'VIDEO',
      image: null,
      video: null,
      fichier: null
    };
  }

  // CRUD operations
  addCourse(): void {
  if (!this.validateForm()) return;

  this.isLoading = true;

  // 1. Build the Course object
  const cours: Course = {
    id_cours: null,
    titre: this.courseForm.titre,
    description: this.courseForm.description,
    duree: this.courseForm.duree,
    niveau: this.courseForm.niveau,
    categorie: this.courseForm.categorie,
    prix: this.courseForm.prix,
    nb_max_etudiant: this.courseForm.nbMaxEtudiant,
    format: this.courseForm.format, 
    image:'',
    fichier:'',
    video:''

  };

  // 2. Extract files
  const imageFile: File = this.courseForm.image;
  const fichierFile: File | undefined = this.courseForm.fichier;
  const videoFile: File | undefined = this.courseForm.video;

  // 3. Call the service
  this.courseService.addCours(cours, imageFile, fichierFile, videoFile).subscribe({
    next: () => {
      this.loadCourses();
      this.closeModals();
      this.isLoading = false;
    },
    error: (error) => {
      console.error('Failed to add course:', error);
      this.errorMessage = 'Failed to add course. Please try again.';
      this.isLoading = false;
    }
  });
}


  updateCourse(): void {
    if (!this.selectedCourse || !this.validateForm()) return;

    this.isLoading = true;
    this.courseService.updateCourse(
      this.selectedCourse.id_cours,
      this.courseForm.titre,
      this.courseForm.description,
      this.courseForm.duree,
      this.courseForm.niveau,
      this.courseForm.categorie,
      this.courseForm.prix,
      this.courseForm.nbMaxEtudiant,
      this.courseForm.format,
      this.courseForm.image,
      this.courseForm.video,
      this.courseForm.fichier
    ).subscribe({
      next: () => {
        this.loadCourses();
        this.closeModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to update course:', error);
        this.errorMessage = 'Failed to update course. Please try again.';
        this.isLoading = false;
      }
    });
  }

  deleteCourse(): void {
    if (!this.selectedCourse) return;

    this.isLoading = true;
    this.courseService.deleteCourse(this.selectedCourse.id_cours).subscribe({
      next: () => {
        this.loadCourses();
        this.closeModals();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to delete course:', error);
        this.errorMessage = 'Failed to delete course. Please try again.';
        this.isLoading = false;
      }
    });
  }

  // Utility methods
  validateForm(): boolean {
    return !!(this.courseForm.titre &&
             this.courseForm.description &&
             this.courseForm.duree &&
             this.courseForm.categorie);
  }

  onImageSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.courseForm.image = file;
    }
  }

  onVideoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.courseForm.video = file;
    }
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.courseForm.fichier = file;
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'ACTIVE': return 'badge-success';
      case 'INACTIVE': return 'badge-danger';
      case 'DRAFT': return 'badge-warning';
      default: return 'badge-secondary';
    }
  }

  getLevelBadgeClass(level: string): string {
    switch (level) {
      case 'BEGINNER': return 'badge-info';
      case 'INTERMEDIATE': return 'badge-warning';
      case 'ADVANCED': return 'badge-danger';
      default: return 'badge-secondary';
    }
  }

  formatPrice(price: number): string {
    return price === 0 ? 'Free' : `$${price}`;
  }
}
