// Sidebar styling with golden theme - maintaining all responsive functionality
.sidenav {
  background-color: #FFFBEB !important;
  border-right: 2px solid rgba(255, 204, 0, 0.2);

  .navbar-brand-img {
    max-height: 2rem;
  }

  .navbar-nav {
    .nav-item {
      .nav-link {
        color: #1C1F21 !important;
        font-weight: 500;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 2px 8px;

        &:hover {
          background-color: rgba(255, 204, 0, 0.1) !important;
          color: #1C1F21 !important;
          transform: translateX(5px);
        }

        &.active {
          background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
          color: #1C1F21 !important;
          font-weight: 600;
          box-shadow: 0 4px 15px rgba(255, 204, 0, 0.3);
        }

        i {
          color: #FFCC00 !important;
          margin-right: 8px;
        }

        .nav-link-text {
          color: inherit !important;
        }
      }
    }
  }

  // Sidenav toggler styling - keep all original functionality
  .sidenav-toggler {
    cursor: pointer;

    .sidenav-toggler-line {
      background-color: #FFCC00 !important;
      transition: all 0.3s ease;
    }

    &:hover .sidenav-toggler-line {
      background-color: #FFDA47 !important;
    }
  }

  // Sidenav header styling
  .sidenav-header {
    padding: 1.5rem 1rem;

    .navbar-brand {
      display: flex;
      align-items: center;
    }
  }
}

// Maintain all responsive states
body {
  // When sidebar is pinned (expanded)
  &.g-sidenav-pinned {
    .sidenav {
      transform: translateX(0);
    }
  }

  // When sidebar is hidden (collapsed)
  &.g-sidenav-hidden {
    .sidenav {
      margin-left: -250px;
    }
  }

  // When sidebar is shown on hover
  &.g-sidenav-show {
    .sidenav {
      transform: translateX(0);
    }
  }
}

// Override any white backgrounds
.bg-white {
  background-color: #FFFBEB !important;
}

// Mobile responsive behavior (keep original functionality)
@media (max-width: 1199px) {
  .sidenav {
    transform: translateX(-100%);

    &.show {
      transform: translateX(0);
    }
  }

  body.g-sidenav-show .sidenav {
    transform: translateX(0);
  }
}