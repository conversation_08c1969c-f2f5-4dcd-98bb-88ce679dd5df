// Sidebar styling with golden theme
.sidenav {
  background-color: #FFFBEB !important;
  border-right: 2px solid rgba(255, 204, 0, 0.2);

  .navbar-brand-img {
    max-height: 2rem;
  }

  .navbar-nav {
    .nav-item {
      .nav-link {
        color: #1C1F21 !important;
        font-weight: 500;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 2px 8px;

        &:hover {
          background-color: rgba(255, 204, 0, 0.1) !important;
          color: #1C1F21 !important;
          transform: translateX(5px);
        }

        &.active {
          background: linear-gradient(135deg, #FFCC00 0%, #FFDA47 100%) !important;
          color: #1C1F21 !important;
          font-weight: 600;
          box-shadow: 0 4px 15px rgba(255, 204, 0, 0.3);
        }

        i {
          color: #FFCC00 !important;
          margin-right: 8px;
        }

        .nav-link-text {
          color: inherit !important;
        }
      }
    }
  }

  // Sidenav toggler styling
  .sidenav-toggler {
    .sidenav-toggler-line {
      background-color: #FFCC00 !important;
    }
  }
}

// Override any white backgrounds
.bg-white {
  background-color: #FFFBEB !important;
}

// Responsive adjustments
@media (max-width: 1199px) {
  .sidenav {
    transform: translateX(-100%);

    &.show {
      transform: translateX(0);
    }
  }
}