import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Category } from '../models/Category';

@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  i:number=0;
  private apiUrl = 'http://localhost:8080/cours';

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Get all categories
  getAllCategories(): Observable<Category[]> {
    return this.http.get<Category[]>(`${this.apiUrl}/retrieveAllCategory`, {
      headers: this.getAuthHeaders()
    });
  }

  // Add new category
  addCategory(title: string): Observable<Category> {
    const category = { titre: title }; // Backend expects 'titre' field
    return this.http.post<Category>(`${this.apiUrl}/addCategory`, category, {
      headers: this.getAuthHeaders()
    });
  }

  // Update category
  updateCategory(id: number, title: string): Observable<Category> {
    const category = { id: id, titre: title }; // Backend expects 'titre' field
    return this.http.put<Category>(`${this.apiUrl}/updateCategory`, category, {
      headers: this.getAuthHeaders()
    });
  }

  // Delete category
  deleteCategory(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/deleteCategory/${id}`, {
      headers: this.getAuthHeaders()
    });
  }
}
