<!-- Loading State -->
<div *ngIf="isLoading" class="container-fluid py-5">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card shadow border-0">
        <div class="card-body text-center py-5">
          <div class="spinner-border text-warning" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <p class="text-muted mt-3">Loading course details...</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Error State -->
<div *ngIf="errorMessage" class="container-fluid py-5">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="alert alert-danger" role="alert">
        {{ errorMessage }}
        <button type="button" class="btn btn-sm btn-outline-danger ml-2" (click)="goBack()">
          <i class="fas fa-arrow-left mr-1"></i>
          Go Back
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Course Details -->
<div *ngIf="course && !isLoading && !errorMessage" class="course-details">
  <!-- Hero Section -->
  <div class="hero-section" style="background: linear-gradient(135deg, #FFFBEB 0%, #FFEFAD 100%);">
    <div class="container-fluid py-5">
      <div class="row">
        <div class="col-lg-8">
          <!-- Breadcrumb -->
          <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb bg-transparent p-0">
              <li class="breadcrumb-item">
                <a href="#/courses" class="text-warning" (click)="goBack()">
                  <i class="fas fa-arrow-left mr-1"></i>
                  Courses
                </a>
              </li>
              <li class="breadcrumb-item active text-dark">{{ course.titre }}</li>
            </ol>
          </nav>

          <!-- Course Header -->
          <div class="course-header mb-4">
            <span class="badge badge-warning mb-2">{{ course.categorie?.title }}</span>
            <h1 class="display-4 font-weight-bold text-dark mb-3">{{ course.titre }}</h1>
            <p class="lead text-muted mb-4">{{ course.description }}</p>
            
            <!-- Course Meta -->
            <div class="course-meta d-flex flex-wrap align-items-center">
              <div class="meta-item mr-4 mb-2">
                <i class="fas fa-clock text-warning mr-2"></i>
                <span class="text-dark">{{ course.duree }}</span>
              </div>
              <div class="meta-item mr-4 mb-2">
                <i class="fas fa-signal text-warning mr-2"></i>
                <span class="text-dark">{{ course.niveau }}</span>
              </div>
              <div class="meta-item mr-4 mb-2">
                <i class="fas fa-users text-warning mr-2"></i>
                <span class="text-dark">{{ course.nb_etudiants_enrolled || 0 }} students</span>
              </div>
              <div class="meta-item mr-4 mb-2">
                <div class="rating d-flex align-items-center">
                  <span *ngFor="let star of getStarArray(course.rating || 0)"
                        class="star mr-1"
                        [class.text-warning]="star"
                        [class.text-muted]="!star">
                    <i class="fas fa-star"></i>
                  </span>
                  <span class="text-dark ml-2">{{ course.rating || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Course Card -->
        <div class="col-lg-4">
          <div class="card shadow-lg border-0 sticky-top" style="top: 20px;">
            <div class="course-image-container">
              <img 
                [src]="course.image || 'assets/img/theme/course-default.jpg'" 
                [alt]="course.titre"
                class="card-img-top"
                style="height: 200px; object-fit: cover;"
                onerror="this.src='assets/img/theme/course-default.jpg'"
              />
            </div>
            <div class="card-body">
              <div class="price-section text-center mb-4">
                <h3 class="price-display mb-0" *ngIf="course.prix > 0">
                  <span class="text-warning font-weight-bold">${{ course.prix }}</span>
                </h3>
                <h3 class="price-display mb-0" *ngIf="course.prix === 0">
                  <span class="text-success font-weight-bold">Free</span>
                </h3>
              </div>

              <!-- Enrollment Button -->
              <div class="enrollment-section" *ngIf="!isEnrolled">
                <button class="btn btn-warning btn-lg btn-block font-weight-bold" 
                        (click)="enrollCourse()">
                  <i class="fas fa-play mr-2"></i>
                  Enroll Now
                </button>
              </div>

              <!-- Progress Section -->
              <div class="progress-section" *ngIf="isEnrolled">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <span class="text-dark font-weight-bold">Progress</span>
                  <span class="text-warning font-weight-bold">{{ progress }}%</span>
                </div>
                <div class="progress mb-3" style="height: 8px;">
                  <div class="progress-bar bg-warning" 
                       [style.width.%]="progress"></div>
                </div>
                <button class="btn btn-outline-warning btn-block font-weight-bold">
                  <i class="fas fa-play mr-2"></i>
                  Continue Learning
                </button>
              </div>

              <!-- Course Features -->
              <div class="course-features mt-4">
                <h6 class="font-weight-bold text-dark mb-3">This course includes:</h6>
                <ul class="list-unstyled">
                  <li class="mb-2" *ngIf="course.video">
                    <i class="fas fa-video text-warning mr-2"></i>
                    <span class="text-dark">Video content</span>
                  </li>
                  <li class="mb-2" *ngIf="course.fichier">
                    <i class="fas fa-file-pdf text-warning mr-2"></i>
                    <span class="text-dark">Downloadable resources</span>
                  </li>
                  <li class="mb-2">
                    <i class="fas fa-mobile-alt text-warning mr-2"></i>
                    <span class="text-dark">Mobile access</span>
                  </li>
                  <li class="mb-2">
                    <i class="fas fa-infinity text-warning mr-2"></i>
                    <span class="text-dark">Lifetime access</span>
                  </li>
                  <li class="mb-2">
                    <i class="fas fa-certificate text-warning mr-2"></i>
                    <span class="text-dark">Certificate of completion</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Course Content Tabs -->
  <div class="container-fluid py-5" style="background-color: #FFFBEB;">
    <div class="row">
      <div class="col-12">
        <!-- Tab Navigation -->
        <ul class="nav nav-pills nav-fill mb-4" style="background-color: white; border-radius: 10px; padding: 10px;">
          <li class="nav-item">
            <a class="nav-link font-weight-bold" 
               [class.active]="activeTab === 'overview'"
               [class.text-warning]="activeTab === 'overview'"
               [class.text-dark]="activeTab !== 'overview'"
               (click)="setActiveTab('overview')"
               style="border-radius: 8px;">
              <i class="fas fa-info-circle mr-2"></i>
              Overview
            </a>
          </li>
          <li class="nav-item" *ngIf="course.video">
            <a class="nav-link font-weight-bold"
               [class.active]="activeTab === 'video'"
               [class.text-warning]="activeTab === 'video'"
               [class.text-dark]="activeTab !== 'video'"
               (click)="setActiveTab('video')"
               style="border-radius: 8px;">
              <i class="fas fa-play mr-2"></i>
              Video Content
            </a>
          </li>
          <li class="nav-item" *ngIf="course.fichier">
            <a class="nav-link font-weight-bold"
               [class.active]="activeTab === 'resources'"
               [class.text-warning]="activeTab === 'resources'"
               [class.text-dark]="activeTab !== 'resources'"
               (click)="setActiveTab('resources')"
               style="border-radius: 8px;">
              <i class="fas fa-download mr-2"></i>
              Resources
            </a>
          </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Overview Tab -->
          <div class="tab-pane fade" [class.show]="activeTab === 'overview'" [class.active]="activeTab === 'overview'">
            <div class="card shadow border-0">
              <div class="card-body p-5">
                <h3 class="font-weight-bold text-dark mb-4">Course Overview</h3>
                <div class="row">
                  <div class="col-md-8">
                    <h5 class="font-weight-bold text-dark mb-3">About this course</h5>
                    <p class="text-muted mb-4">{{ course.description }}</p>
                    
                    <h5 class="font-weight-bold text-dark mb-3">What you'll learn</h5>
                    <ul class="list-unstyled">
                      <li class="mb-2">
                        <i class="fas fa-check text-success mr-2"></i>
                        <span class="text-dark">Master the fundamentals of {{ course.titre }}</span>
                      </li>
                      <li class="mb-2">
                        <i class="fas fa-check text-success mr-2"></i>
                        <span class="text-dark">Apply practical skills in real-world scenarios</span>
                      </li>
                      <li class="mb-2">
                        <i class="fas fa-check text-success mr-2"></i>
                        <span class="text-dark">Build confidence in {{ course.categorie?.title }} concepts</span>
                      </li>
                      <li class="mb-2">
                        <i class="fas fa-check text-success mr-2"></i>
                        <span class="text-dark">Complete hands-on projects and exercises</span>
                      </li>
                    </ul>
                  </div>
                  <div class="col-md-4">
                    <div class="course-stats bg-light p-4 rounded">
                      <h6 class="font-weight-bold text-dark mb-3">Course Details</h6>
                      <div class="stat-item mb-3">
                        <strong class="text-dark">Duration:</strong>
                        <span class="text-muted ml-2">{{ course.duree }}</span>
                      </div>
                      <div class="stat-item mb-3">
                        <strong class="text-dark">Level:</strong>
                        <span class="text-muted ml-2">{{ course.niveau }}</span>
                      </div>
                      <div class="stat-item mb-3">
                        <strong class="text-dark">Format:</strong>
                        <span class="text-muted ml-2">{{ course.format || 'Mixed' }}</span>
                      </div>
                      <div class="stat-item mb-3">
                        <strong class="text-dark">Students:</strong>
                        <span class="text-muted ml-2">{{ course.nb_etudiants_enrolled || 0 }}</span>
                      </div>
                      <div class="stat-item">
                        <strong class="text-dark">Max Capacity:</strong>
                        <span class="text-muted ml-2">{{ course.nb_max_etudiant }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Video Content Tab -->
          <div class="tab-pane fade" [class.show]="activeTab === 'video'" [class.active]="activeTab === 'video'" *ngIf="course.video">
            <div class="card shadow border-0">
              <div class="card-body p-5">
                <h3 class="font-weight-bold text-dark mb-4">Video Content</h3>
                <div class="video-container mb-4">
                  <video controls class="w-100" style="max-height: 500px; border-radius: 10px;">
                    <source [src]="course.video" type="video/mp4">
                    Your browser does not support the video tag.
                  </video>
                </div>
                <div class="video-actions">
                  <button class="btn btn-outline-warning mr-3" (click)="downloadFile('video')">
                    <i class="fas fa-download mr-2"></i>
                    Download Video
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Resources Tab -->
          <div class="tab-pane fade" [class.show]="activeTab === 'resources'" [class.active]="activeTab === 'resources'" *ngIf="course.fichier">
            <div class="card shadow border-0">
              <div class="card-body p-5">
                <h3 class="font-weight-bold text-dark mb-4">Course Resources</h3>
                <div class="resources-list">
                  <div class="resource-item d-flex justify-content-between align-items-center p-4 mb-3 bg-light rounded">
                    <div class="resource-info">
                      <h5 class="font-weight-bold text-dark mb-1">
                        <i class="fas fa-file-pdf text-danger mr-2"></i>
                        Course Materials
                      </h5>
                      <p class="text-muted mb-0">PDF document with course content and exercises</p>
                    </div>
                    <button class="btn btn-warning" (click)="downloadFile('pdf')">
                      <i class="fas fa-download mr-2"></i>
                      Download
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
