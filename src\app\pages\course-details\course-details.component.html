<!-- Loading State -->
<div *ngIf="isLoading" class="loading-container">
  <div class="loading-content">
    <div class="loading-spinner"></div>
    <h4>Loading Course...</h4>
    <p>Please wait while we fetch the course details</p>
  </div>
</div>

<!-- Error State -->
<div *ngIf="errorMessage" class="error-container">
  <div class="error-content">
    <i class="fas fa-exclamation-triangle"></i>
    <h4>Oops! Something went wrong</h4>
    <p>{{ errorMessage }}</p>
    <button class="btn-primary" (click)="goBack()">
      <i class="fas fa-arrow-left"></i>
      Go Back to Courses
    </button>
  </div>
</div>

<!-- Course Details -->
<div *ngIf="course && !isLoading && !errorMessage" class="course-details-page">

  <!-- Navigation Bar -->
  <div class="course-nav">
    <div class="nav-content">
      <button class="back-btn" (click)="goBack()">
        <i class="fas fa-arrow-left"></i>
        <span>Back to Courses</span>
      </button>
      <div class="course-progress" *ngIf="isEnrolled">
        <span class="progress-text">Progress: {{ progress }}%</span>
        <div class="progress-bar">
          <div class="progress-fill" [style.width.%]="progress"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Course Header -->
  <div class="course-header">
    <div class="header-content">
      <div class="course-info">
        <div class="category-badge">{{ course.categorie?.title }}</div>
        <h1 class="course-title">{{ course.titre }}</h1>
        <p class="course-description">{{ course.description }}</p>

        <div class="course-stats">
          <div class="stat-item">
            <i class="fas fa-clock"></i>
            <span>{{ course.duree }}</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-signal"></i>
            <span>{{ course.niveau }}</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-users"></i>
            <span>{{ course.nb_etudiants_enrolled || 0 }} students</span>
          </div>
          <div class="stat-item">
            <div class="rating">
              <span *ngFor="let star of getStarArray(course.rating || 0)"
                    class="star"
                    [class.filled]="star">
                <i class="fas fa-star"></i>
              </span>
              <span class="rating-number">{{ course.rating || 0 }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="course-card">
        <div class="course-image">
          <img [src]="course.image || 'assets/img/theme/course-default.jpg'"
               [alt]="course.titre"
               onerror="this.src='assets/img/theme/course-default.jpg'">
        </div>

        <div class="course-pricing">
          <div class="price" *ngIf="course.prix > 0">${{ course.prix }}</div>
          <div class="price free" *ngIf="course.prix === 0">Free</div>
        </div>

        <div class="enrollment-section">
          <button class="enroll-btn"
                  *ngIf="!isEnrolled"
                  (click)="enrollCourse()">
            <i class="fas fa-play"></i>
            Enroll Now
          </button>

          <div class="enrolled-status" *ngIf="isEnrolled">
            <i class="fas fa-check-circle"></i>
            <span>You're enrolled!</span>
          </div>
        </div>

        <div class="course-includes">
          <h4>This course includes:</h4>
          <ul>
            <li *ngIf="course.video">
              <i class="fas fa-video"></i>
              Video content
            </li>
            <li *ngIf="course.fichier">
              <i class="fas fa-file-pdf"></i>
              Downloadable resources
            </li>
            <li>
              <i class="fas fa-mobile-alt"></i>
              Mobile access
            </li>
            <li>
              <i class="fas fa-infinity"></i>
              Lifetime access
            </li>
            <li>
              <i class="fas fa-certificate"></i>
              Certificate
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Course Content Section -->
  <div class="course-content" *ngIf="isEnrolled">
    <div class="content-container">

      <!-- Content Navigation -->
      <div class="content-nav">
        <button class="nav-btn"
                [class.active]="activeTab === 'overview'"
                (click)="setActiveTab('overview')">
          <i class="fas fa-info-circle"></i>
          <span>Overview</span>
        </button>

        <button class="nav-btn"
                *ngIf="course.video"
                [class.active]="activeTab === 'video'"
                (click)="setActiveTab('video')">
          <i class="fas fa-play"></i>
          <span>Video</span>
        </button>

        <button class="nav-btn"
                *ngIf="course.fichier"
                [class.active]="activeTab === 'materials'"
                (click)="setActiveTab('materials')">
          <i class="fas fa-file-pdf"></i>
          <span>Materials</span>
        </button>
      </div>

      <!-- Content Display -->
      <div class="content-display">

        <!-- Overview Content -->
        <div class="content-section" *ngIf="activeTab === 'overview'">
          <div class="overview-content">
            <h2>About This Course</h2>
            <p>{{ course.description }}</p>

            <div class="learning-objectives">
              <h3>What You'll Learn</h3>
              <ul>
                <li>Master the fundamentals of {{ course.titre }}</li>
                <li>Apply practical skills in real-world scenarios</li>
                <li>Build confidence in {{ course.categorie?.title }} concepts</li>
                <li>Complete hands-on projects and exercises</li>
              </ul>
            </div>

            <div class="course-details">
              <h3>Course Details</h3>
              <div class="details-grid">
                <div class="detail-item">
                  <span class="label">Duration:</span>
                  <span class="value">{{ course.duree }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">Level:</span>
                  <span class="value">{{ course.niveau }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">Format:</span>
                  <span class="value">{{ course.format || 'Mixed' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">Students:</span>
                  <span class="value">{{ course.nb_etudiants_enrolled || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Video Content -->
        <div class="content-section" *ngIf="activeTab === 'video' && course.video">
          <div class="video-content">
            <h2>Course Video</h2>
            <div class="video-player">
              <video controls>
                <source [src]="course.video" type="video/mp4">
                Your browser does not support the video tag.
              </video>
            </div>
            <div class="video-actions">
              <button class="download-btn" (click)="downloadFile('video')">
                <i class="fas fa-download"></i>
                Download Video
              </button>
            </div>
          </div>
        </div>

        <!-- Materials Content -->
        <div class="content-section" *ngIf="activeTab === 'materials' && course.fichier">
          <div class="materials-content">
            <h2>Course Materials</h2>
            <div class="materials-list">
              <div class="material-item">
                <div class="material-info">
                  <i class="fas fa-file-pdf"></i>
                  <div class="info">
                    <h4>Course Materials</h4>
                    <p>PDF document with course content, exercises, and additional resources</p>
                  </div>
                </div>
                <button class="download-btn" (click)="downloadFile('pdf')">
                  <i class="fas fa-download"></i>
                  Download PDF
                </button>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>

  <!-- Enrollment Prompt for Non-Enrolled Users -->
  <div class="enrollment-prompt" *ngIf="!isEnrolled">
    <div class="prompt-content">
      <h2>Ready to Start Learning?</h2>
      <p>Enroll now to access all course content including videos, materials, and more!</p>
      <button class="enroll-btn-large" (click)="enrollCourse()">
        <i class="fas fa-play"></i>
        Enroll in This Course
      </button>
    </div>
  </div>

</div>
