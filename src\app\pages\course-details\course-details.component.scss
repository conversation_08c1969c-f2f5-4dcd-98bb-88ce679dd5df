// Color Palette
$primary-yellow: #FFCC00;
$light-yellow: #FFDA47;
$pale-yellow: #FFEFAD;
$cream: #FFFBEB;
$dark: #1C1F21;
$white: #FFFFFF;
$gray: #6c757d;
$light-gray: #f8f9fa;

// Loading State
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, $cream 0%, $pale-yellow 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    text-align: center;
    color: $dark;

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid $pale-yellow;
      border-top: 4px solid $primary-yellow;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    h4 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 10px;
    }

    p {
      color: $gray;
      font-size: 1rem;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Error State
.error-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, $cream 0%, $pale-yellow 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .error-content {
    text-align: center;
    color: $dark;
    max-width: 400px;
    padding: 40px;

    i {
      font-size: 4rem;
      color: #dc3545;
      margin-bottom: 20px;
    }

    h4 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 15px;
    }

    p {
      color: $gray;
      margin-bottom: 25px;
    }

    .btn-primary {
      background: $primary-yellow;
      border: none;
      color: $dark;
      padding: 12px 24px;
      border-radius: 25px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        background: $light-yellow;
        transform: translateY(-2px);
      }

      i {
        font-size: 1rem;
        margin-right: 8px;
      }
    }
  }
}

// Main Course Details Page
.course-details-page {
  min-height: 100vh;
  background: linear-gradient(135deg, $cream 0%, $pale-yellow 100%);

  // Navigation Bar
  .course-nav {
    background: $white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;

    .nav-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .back-btn {
        background: none;
        border: none;
        color: $dark;
        font-size: 1rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          color: $primary-yellow;
        }

        i {
          font-size: 1.1rem;
        }
      }

      .course-progress {
        display: flex;
        align-items: center;
        gap: 15px;

        .progress-text {
          font-weight: 600;
          color: $dark;
        }

        .progress-bar {
          width: 200px;
          height: 8px;
          background: $light-gray;
          border-radius: 4px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, $primary-yellow 0%, $light-yellow 100%);
            transition: width 0.3s ease;
          }
        }
      }
    }
  }

  // Course Header
  .course-header {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;

    .header-content {
      display: grid;
      grid-template-columns: 1fr 400px;
      gap: 40px;
      align-items: start;

      .course-info {
        .category-badge {
          display: inline-block;
          background: $primary-yellow;
          color: $dark;
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 0.9rem;
          font-weight: 600;
          margin-bottom: 20px;
        }

        .course-title {
          font-size: 3rem;
          font-weight: 800;
          color: $dark;
          line-height: 1.2;
          margin-bottom: 20px;
        }

        .course-description {
          font-size: 1.2rem;
          color: $gray;
          line-height: 1.6;
          margin-bottom: 30px;
        }

        .course-stats {
          display: flex;
          flex-wrap: wrap;
          gap: 30px;

          .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: $dark;
            font-weight: 500;

            i {
              color: $primary-yellow;
              font-size: 1.1rem;
            }

            .rating {
              display: flex;
              align-items: center;
              gap: 5px;

              .star {
                color: $light-gray;
                font-size: 1rem;

                &.filled {
                  color: $primary-yellow;
                }
              }

              .rating-number {
                margin-left: 5px;
                font-weight: 600;
              }
            }
          }
        }
      }

      .course-card {
        background: $white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: sticky;
        top: 100px;

        .course-image {
          width: 100%;
          height: 250px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .course-pricing {
          padding: 20px;
          text-align: center;
          border-bottom: 1px solid $light-gray;

          .price {
            font-size: 2.5rem;
            font-weight: 800;
            color: $primary-yellow;

            &.free {
              color: #28a745;
            }
          }
        }

        .enrollment-section {
          padding: 20px;

          .enroll-btn {
            width: 100%;
            background: linear-gradient(135deg, $primary-yellow 0%, $light-yellow 100%);
            border: none;
            color: $dark;
            padding: 15px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(255, 204, 0, 0.4);
            }

            i {
              font-size: 1.2rem;
            }
          }

          .enrolled-status {
            text-align: center;
            color: #28a745;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            i {
              font-size: 1.2rem;
            }
          }
        }

        .course-includes {
          padding: 20px;
          border-top: 1px solid $light-gray;

          h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: $dark;
            margin-bottom: 15px;
          }

          ul {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
              display: flex;
              align-items: center;
              gap: 10px;
              padding: 8px 0;
              color: $gray;

              i {
                color: $primary-yellow;
                width: 16px;
              }
            }
          }
        }
      }
    }
  }

  // Course Content Section
  .course-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;

    .content-container {
      background: $white;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .content-nav {
        display: flex;
        background: $light-gray;
        border-bottom: 1px solid #dee2e6;

        .nav-btn {
          flex: 1;
          background: none;
          border: none;
          padding: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;
          color: $gray;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 204, 0, 0.1);
            color: $primary-yellow;
          }

          &.active {
            background: $primary-yellow;
            color: $dark;
            font-weight: 600;
          }

          i {
            font-size: 1.1rem;
          }
        }
      }

      .content-display {
        .content-section {
          padding: 40px;

          h2 {
            font-size: 2rem;
            font-weight: 700;
            color: $dark;
            margin-bottom: 20px;
          }

          h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: $dark;
            margin: 30px 0 15px;
          }

          p {
            font-size: 1.1rem;
            line-height: 1.6;
            color: $gray;
            margin-bottom: 20px;
          }

          // Overview Content
          .overview-content {
            .learning-objectives {
              ul {
                list-style: none;
                padding: 0;

                li {
                  display: flex;
                  align-items: center;
                  gap: 10px;
                  padding: 8px 0;
                  font-size: 1.1rem;
                  color: $dark;

                  &::before {
                    content: "✓";
                    background: #28a745;
                    color: white;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 0.8rem;
                    font-weight: bold;
                  }
                }
              }
            }

            .course-details {
              .details-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-top: 20px;

                .detail-item {
                  background: $light-gray;
                  padding: 15px;
                  border-radius: 10px;

                  .label {
                    font-weight: 600;
                    color: $dark;
                    display: block;
                    margin-bottom: 5px;
                  }

                  .value {
                    color: $primary-yellow;
                    font-weight: 500;
                  }
                }
              }
            }
          }

          // Video Content
          .video-content {
            .video-player {
              margin: 20px 0;
              border-radius: 15px;
              overflow: hidden;
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

              video {
                width: 100%;
                height: auto;
                max-height: 500px;
              }
            }

            .video-actions {
              margin-top: 20px;
            }
          }

          // Materials Content
          .materials-content {
            .materials-list {
              margin-top: 20px;

              .material-item {
                background: $light-gray;
                border-radius: 15px;
                padding: 25px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 15px;

                .material-info {
                  display: flex;
                  align-items: center;
                  gap: 15px;

                  i {
                    font-size: 2.5rem;
                    color: #dc3545;
                  }

                  .info {
                    h4 {
                      font-size: 1.2rem;
                      font-weight: 600;
                      color: $dark;
                      margin: 0 0 5px;
                    }

                    p {
                      color: $gray;
                      margin: 0;
                      font-size: 0.95rem;
                    }
                  }
                }
              }
            }
          }

          // Download Button
          .download-btn {
            background: linear-gradient(135deg, $primary-yellow 0%, $light-yellow 100%);
            border: none;
            color: $dark;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(255, 204, 0, 0.4);
            }

            i {
              font-size: 1rem;
            }
          }
        }
      }
    }
  }

  // Enrollment Prompt
  .enrollment-prompt {
    max-width: 1200px;
    margin: 40px auto;
    padding: 0 20px;

    .prompt-content {
      background: linear-gradient(135deg, $primary-yellow 0%, $light-yellow 100%);
      padding: 60px 40px;
      border-radius: 20px;
      text-align: center;
      color: $dark;

      h2 {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 15px;
      }

      p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.8;
      }

      .enroll-btn-large {
        background: $dark;
        color: $white;
        border: none;
        padding: 18px 40px;
        border-radius: 15px;
        font-size: 1.2rem;
        font-weight: 700;
        display: inline-flex;
        align-items: center;
        gap: 12px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 10px 30px rgba(28, 31, 33, 0.3);
        }

        i {
          font-size: 1.3rem;
        }
      }
    }
  }
}

    .course-features {
      ul {
        li {
          display: flex;
          align-items: center;
          padding: 8px 0;

          i {
            width: 20px;
            text-align: center;
          }
        }
      }
    }
  }

  .nav-pills {
    background-color: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    .nav-link {
      color: $dark;
      font-weight: 600;
      padding: 12px 24px;
      border-radius: 8px !important;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(255, 204, 0, 0.1);
        color: $primary-yellow;
      }

      &.active {
        background-color: $primary-yellow !important;
        color: $dark !important;
        box-shadow: 0 2px 8px rgba(255, 204, 0, 0.3);
      }
    }
  }

  .tab-content {
    .card {
      .card-body {
        h3, h5, h6 {
          color: $dark;
        }

        .course-stats {
          background-color: rgba(255, 239, 173, 0.3) !important;
          border-radius: 10px;

          .stat-item {
            border-bottom: 1px solid rgba(255, 204, 0, 0.2);
            padding-bottom: 8px;

            &:last-child {
              border-bottom: none;
              padding-bottom: 0;
            }

            strong {
              color: $dark;
            }
          }
        }

        .video-container {
          video {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          }
        }

        .resource-item {
          background-color: rgba(255, 239, 173, 0.3) !important;
          border-radius: 10px;
          border: 1px solid rgba(255, 204, 0, 0.2);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 204, 0, 0.2);
          }

          .resource-info {
            h5 {
              color: $dark;
              margin-bottom: 4px;

              i {
                color: #dc3545;
              }
            }

            p {
              color: #6c757d;
              font-size: 0.9rem;
            }
          }
        }
      }
    }
  }

  .sticky-top {
    top: 20px !important;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .course-details-page {
    .course-header {
      .header-content {
        grid-template-columns: 1fr;
        gap: 30px;

        .course-card {
          position: relative;
          top: 0;
        }
      }
    }

    .course-content {
      .content-container {
        .content-nav {
          .nav-btn {
            padding: 15px 10px;
            font-size: 0.9rem;

            span {
              display: none;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .course-details-page {
    .course-nav {
      .nav-content {
        flex-direction: column;
        gap: 15px;

        .course-progress {
          .progress-bar {
            width: 150px;
          }
        }
      }
    }

    .course-header {
      padding: 20px;

      .header-content {
        .course-info {
          .course-title {
            font-size: 2rem;
          }

          .course-stats {
            flex-direction: column;
            gap: 15px;
          }
        }
      }
    }

    .course-content {
      padding: 20px;

      .content-container {
        .content-display {
          .content-section {
            padding: 20px;

            h2 {
              font-size: 1.5rem;
            }
          }
        }
      }
    }

    .enrollment-prompt {
      padding: 0 20px;

      .prompt-content {
        padding: 40px 20px;

        h2 {
          font-size: 1.8rem;
        }

        .enroll-btn-large {
          padding: 15px 30px;
          font-size: 1.1rem;
        }
      }
    }
  }
}

// Animation Classes
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
