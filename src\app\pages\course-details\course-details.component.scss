// Color Palette
$primary-yellow: #FFCC00;
$light-yellow: #FFDA47;
$pale-yellow: #FFEFAD;
$cream: #FFFBEB;
$dark: #1C1F21;

.course-details {
  min-height: 100vh;
  background-color: $cream;

  .hero-section {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 251, 235, 0.9) 0%, rgba(255, 239, 173, 0.9) 100%);
      z-index: 1;
    }

    .container-fluid {
      position: relative;
      z-index: 2;
    }
  }

  .breadcrumb {
    .breadcrumb-item {
      a {
        color: $primary-yellow;
        text-decoration: none;
        font-weight: 500;

        &:hover {
          color: $light-yellow;
          text-decoration: underline;
        }
      }

      &.active {
        color: $dark;
        font-weight: 600;
      }
    }
  }

  .course-header {
    .badge-warning {
      background-color: $primary-yellow;
      color: $dark;
      font-weight: 600;
      padding: 8px 16px;
      border-radius: 20px;
    }

    .display-4 {
      color: $dark;
      line-height: 1.2;
    }

    .lead {
      font-size: 1.1rem;
      line-height: 1.6;
    }

    .course-meta {
      .meta-item {
        display: flex;
        align-items: center;
        font-weight: 500;

        i {
          font-size: 1.1rem;
        }

        .rating {
          .star {
            font-size: 1rem;
          }
        }
      }
    }
  }

  .card {
    border-radius: 15px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .course-image-container {
      overflow: hidden;
      border-radius: 15px 15px 0 0;

      img {
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }
    }

    .price-display {
      font-size: 2rem;
    }

    .btn-warning {
      background-color: $primary-yellow;
      border-color: $primary-yellow;
      color: $dark;
      font-weight: 600;
      border-radius: 10px;
      padding: 12px 24px;
      transition: all 0.3s ease;

      &:hover {
        background-color: $light-yellow;
        border-color: $light-yellow;
        color: $dark;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 204, 0, 0.3);
      }
    }

    .btn-outline-warning {
      border-color: $primary-yellow;
      color: $primary-yellow;
      font-weight: 600;
      border-radius: 10px;
      padding: 12px 24px;
      transition: all 0.3s ease;

      &:hover {
        background-color: $primary-yellow;
        border-color: $primary-yellow;
        color: $dark;
        transform: translateY(-1px);
      }
    }

    .progress {
      border-radius: 10px;
      background-color: rgba(255, 204, 0, 0.2);

      .progress-bar {
        border-radius: 10px;
        background-color: $primary-yellow;
      }
    }

    .course-features {
      ul {
        li {
          display: flex;
          align-items: center;
          padding: 8px 0;

          i {
            width: 20px;
            text-align: center;
          }
        }
      }
    }
  }

  .nav-pills {
    background-color: white !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    .nav-link {
      color: $dark;
      font-weight: 600;
      padding: 12px 24px;
      border-radius: 8px !important;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(255, 204, 0, 0.1);
        color: $primary-yellow;
      }

      &.active {
        background-color: $primary-yellow !important;
        color: $dark !important;
        box-shadow: 0 2px 8px rgba(255, 204, 0, 0.3);
      }
    }
  }

  .tab-content {
    .card {
      .card-body {
        h3, h5, h6 {
          color: $dark;
        }

        .course-stats {
          background-color: rgba(255, 239, 173, 0.3) !important;
          border-radius: 10px;

          .stat-item {
            border-bottom: 1px solid rgba(255, 204, 0, 0.2);
            padding-bottom: 8px;

            &:last-child {
              border-bottom: none;
              padding-bottom: 0;
            }

            strong {
              color: $dark;
            }
          }
        }

        .video-container {
          video {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          }
        }

        .resource-item {
          background-color: rgba(255, 239, 173, 0.3) !important;
          border-radius: 10px;
          border: 1px solid rgba(255, 204, 0, 0.2);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 204, 0, 0.2);
          }

          .resource-info {
            h5 {
              color: $dark;
              margin-bottom: 4px;

              i {
                color: #dc3545;
              }
            }

            p {
              color: #6c757d;
              font-size: 0.9rem;
            }
          }
        }
      }
    }
  }

  .sticky-top {
    top: 20px !important;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .course-details {
    .hero-section {
      .course-header {
        .display-4 {
          font-size: 2rem;
        }

        .course-meta {
          flex-direction: column;
          align-items: flex-start;

          .meta-item {
            margin-right: 0 !important;
            margin-bottom: 8px;
          }
        }
      }
    }

    .sticky-top {
      position: relative !important;
      top: 0 !important;
    }

    .nav-pills {
      .nav-link {
        padding: 8px 12px;
        font-size: 0.9rem;
      }
    }
  }
}

// Animation Classes
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
