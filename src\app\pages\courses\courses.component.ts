import { Component, OnInit } from '@angular/core';
import { Course } from 'src/app/core/models/Course';
import { CourseService } from 'src/app/core/services/course.service';

export interface StudentCourse extends Course {
  isEnrolled?: boolean;
  progress?: number;
}

@Component({
  selector: 'app-courses',
  templateUrl: './courses.component.html',
  styleUrls: ['./courses.component.scss']
})
export class CoursesComponent implements OnInit {

  courses: StudentCourse[] = [];
  filteredCourses: StudentCourse[] = [];
  selectedCategory: string = 'All';
  searchTerm: string = '';
  isLoading: boolean = false;
  errorMessage: string = '';

  categories: string[] = ['All', 'Programming', 'Design', 'Business', 'Marketing', 'Data Science', 'Languages', 'Other'];

  constructor(private courseService: CourseService) { }

  ngOnInit(): void {
    this.loadCourses();
  }

  loadCourses(): void {
    this.isLoading = true;
    this.courseService.getAllCourses().subscribe({
      next: (data: Course[]) => {
        // Convert Course to StudentCourse and add student-specific properties
        this.courses = data.map(course => ({
          ...course,
          isEnrolled: false, // You can check enrollment status from another API
          progress: 0 // You can get progress from another API
        }));
        this.filteredCourses = [...this.courses];
        this.updateCategories();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to load courses:', error);
        this.errorMessage = 'Failed to load courses. Please try again.';
        this.isLoading = false;
      }
    });
  }

  updateCategories(): void {
    // Extract unique categories from loaded courses
    const uniqueCategories = [...new Set(this.courses.map(course => course.categorie))];
    this.categories = ['All', ...uniqueCategories];
  }

  filterCourses(): void {
    this.filteredCourses = this.courses.filter(course => {
      const matchesCategory = this.selectedCategory === 'All' || course.categorie === this.selectedCategory;
      const matchesSearch = course.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                           course.description.toLowerCase().includes(this.searchTerm.toLowerCase());
      return matchesCategory && matchesSearch;
    });
  }

  onCategoryChange(category: string): void {
    this.selectedCategory = category;
    this.filterCourses();
  }

  onSearchChange(): void {
    this.filterCourses();
  }

  enrollCourse(course: StudentCourse): void {
    course.isEnrolled = true;
    course.progress = 0;
    // Here you would typically call an API to enroll the student
    console.log(`Enrolled in course: ${course.titre}`);
  }

  continueCourse(course: StudentCourse): void {
    // Here you would typically navigate to the course content
    console.log(`Continue course: ${course.titre}`);
  }

  getStarArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= Math.floor(rating));
    }
    return stars;
  }
}
