import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Course } from 'src/app/core/models/Course';
import { CourseService } from 'src/app/core/services/course.service';
import { Category } from 'src/app/core/models/Category';
import { CategoryService } from 'src/app/core/services/category.service';

export interface StudentCourse extends Course {
  isEnrolled?: boolean;
  progress?: number;
  categoryName?: string; // For display purposes
}

@Component({
  selector: 'app-courses',
  templateUrl: './courses.component.html',
  styleUrls: ['./courses.component.scss']
})
export class CoursesComponent implements OnInit {

  courses: StudentCourse[] = [];
  filteredCourses: StudentCourse[] = [];
  selectedCategory: string = 'All';
  searchTerm: string = '';
  isLoading: boolean = false;
  errorMessage: string = '';
  categoriesList: Category[] = [];

  categories: string[] = ['All'];

  constructor(
    private courseService: CourseService,
    private categoryService: CategoryService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadCategories();
  }

  loadCategories(): void {
    this.categoryService.getAllCategories().subscribe({
      next: (categories) => {
        this.categoriesList = categories;
        this.loadCourses();
      },
      error: (error) => {
        console.error('Failed to load categories:', error);
        this.loadCourses(); // Load courses anyway
      }
    });
  }

  loadCourses(): void {
    this.isLoading = true;
    this.courseService.getAllCourses().subscribe({
      next: (data: Course[]) => {
        // Convert Course to StudentCourse and add student-specific properties
        this.courses = data.map(course => {
          return {
            ...course,
            isEnrolled: false, // You can check enrollment status from another API
            progress: 0, // You can get progress from another API
            categoryName: course.categorie?.title || 'Unknown' // Category is now an entity
          };
        });
        this.filteredCourses = [...this.courses];
        this.updateCategories();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Failed to load courses:', error);
        this.errorMessage = 'Failed to load courses. Please try again.';
        this.isLoading = false;
      }
    });
  }

  updateCategories(): void {
    // Extract unique category names from loaded courses
    const uniqueCategories = [...new Set(this.courses.map(course => course.categoryName))].filter(cat => cat); // Filter out empty/null categories
    this.categories = ['All', ...uniqueCategories];
  }

  filterCourses(): void {
    this.filteredCourses = this.courses.filter(course => {
      const matchesCategory = this.selectedCategory === 'All' || course.categoryName === this.selectedCategory;
      const matchesSearch = course.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                           course.description.toLowerCase().includes(this.searchTerm.toLowerCase());
      return matchesCategory && matchesSearch;
    });
  }

  onCategoryChange(category: string): void {
    this.selectedCategory = category;
    this.filterCourses();
  }

  onSearchChange(): void {
    this.filterCourses();
  }

  enrollCourse(course: StudentCourse): void {
    course.isEnrolled = true;
    course.progress = 0;
    // Here you would typically call an API to enroll the student
    console.log(`Enrolled in course: ${course.titre}`);
  }

  continueCourse(course: StudentCourse): void {
    // Here you would typically navigate to the course content
    console.log(`Continue course: ${course.titre}`);
  }

  getStarArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= Math.floor(rating));
    }
    return stars;
  }

  viewCourseDetails(course: StudentCourse): void {
    this.router.navigate(['/course', course.id_cours]);
  }
}
