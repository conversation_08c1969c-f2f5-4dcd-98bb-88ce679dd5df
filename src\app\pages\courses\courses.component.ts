import { Component, OnInit } from '@angular/core';

export interface Course {
  id: number;
  title: string;
  description: string;
  instructor: string;
  duration: string;
  level: string;
  price: number;
  rating: number;
  studentsEnrolled: number;
  image: string;
  category: string;
  tags: string[];
  isEnrolled: boolean;
  progress?: number;
}

@Component({
  selector: 'app-courses',
  templateUrl: './courses.component.html',
  styleUrls: ['./courses.component.scss']
})
export class CoursesComponent implements OnInit {

  courses: Course[] = [
    {
      id: 1,
      title: "Angular Complete Guide",
      description: "Master Angular from basics to advanced concepts. Build real-world applications with the latest Angular features.",
      instructor: "<PERSON>. <PERSON>",
      duration: "12 weeks",
      level: "Intermediate",
      price: 299,
      rating: 4.8,
      studentsEnrolled: 1250,
      image: "assets/img/courses/angular.jpg",
      category: "Web Development",
      tags: ["Angular", "TypeScript", "Frontend"],
      isEnrolled: true,
      progress: 65
    },
    {
      id: 2,
      title: "React & Redux Masterclass",
      description: "Learn React and Redux from scratch. Build modern web applications with hooks, context, and state management.",
      instructor: "<PERSON><PERSON>",
      duration: "10 weeks",
      level: "Beginner",
      price: 249,
      rating: 4.9,
      studentsEnrolled: 2100,
      image: "assets/img/courses/react.jpg",
      category: "Web Development",
      tags: ["React", "Redux", "JavaScript"],
      isEnrolled: false
    },
    {
      id: 3,
      title: "Node.js Backend Development",
      description: "Build scalable backend applications with Node.js, Express, and MongoDB. Learn API development and authentication.",
      instructor: "Dr. Emily Rodriguez",
      duration: "8 weeks",
      level: "Intermediate",
      price: 199,
      rating: 4.7,
      studentsEnrolled: 890,
      image: "assets/img/courses/nodejs.jpg",
      category: "Backend Development",
      tags: ["Node.js", "Express", "MongoDB"],
      isEnrolled: true,
      progress: 30
    },
    {
      id: 4,
      title: "Python Data Science",
      description: "Dive into data science with Python. Learn pandas, numpy, matplotlib, and machine learning fundamentals.",
      instructor: "Dr. James Wilson",
      duration: "14 weeks",
      level: "Beginner",
      price: 349,
      rating: 4.6,
      studentsEnrolled: 1800,
      image: "assets/img/courses/python.jpg",
      category: "Data Science",
      tags: ["Python", "Data Science", "Machine Learning"],
      isEnrolled: false
    },
    {
      id: 5,
      title: "UI/UX Design Fundamentals",
      description: "Learn the principles of user interface and user experience design. Create beautiful and functional designs.",
      instructor: "Prof. Lisa Anderson",
      duration: "6 weeks",
      level: "Beginner",
      price: 179,
      rating: 4.9,
      studentsEnrolled: 950,
      image: "assets/img/courses/uiux.jpg",
      category: "Design",
      tags: ["UI/UX", "Design", "Figma"],
      isEnrolled: true,
      progress: 85
    },
    {
      id: 6,
      title: "DevOps with Docker & Kubernetes",
      description: "Master containerization and orchestration. Deploy applications using Docker, Kubernetes, and CI/CD pipelines.",
      instructor: "Dr. Robert Kim",
      duration: "10 weeks",
      level: "Advanced",
      price: 399,
      rating: 4.8,
      studentsEnrolled: 650,
      image: "assets/img/courses/devops.jpg",
      category: "DevOps",
      tags: ["Docker", "Kubernetes", "DevOps"],
      isEnrolled: false
    }
  ];

  filteredCourses: Course[] = [];
  selectedCategory: string = 'All';
  searchTerm: string = '';

  categories: string[] = ['All', 'Web Development', 'Backend Development', 'Data Science', 'Design', 'DevOps'];

  constructor() { }

  ngOnInit(): void {
    this.filteredCourses = [...this.courses];
  }

  filterCourses(): void {
    this.filteredCourses = this.courses.filter(course => {
      const matchesCategory = this.selectedCategory === 'All' || course.category === this.selectedCategory;
      const matchesSearch = course.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                           course.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                           course.instructor.toLowerCase().includes(this.searchTerm.toLowerCase());
      return matchesCategory && matchesSearch;
    });
  }

  onCategoryChange(category: string): void {
    this.selectedCategory = category;
    this.filterCourses();
  }

  onSearchChange(): void {
    this.filterCourses();
  }

  enrollCourse(course: Course): void {
    course.isEnrolled = true;
    course.progress = 0;
    // Here you would typically call an API to enroll the student
    console.log(`Enrolled in course: ${course.title}`);
  }

  continueCourse(course: Course): void {
    // Here you would typically navigate to the course content
    console.log(`Continue course: ${course.title}`);
  }

  getStarArray(rating: number): boolean[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= Math.floor(rating));
    }
    return stars;
  }
}
